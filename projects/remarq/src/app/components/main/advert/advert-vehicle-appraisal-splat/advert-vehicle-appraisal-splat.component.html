<!--<div #hovercolor style="text-align: center"></div>-->
<div
  *ngIf="showExplanation"
  class="attention-box px-3 py-2 mb-2" style="font-size: 0.875rem; font-weight: 400"
  (click)="showExplanation = !showExplanation">
  <div class="d-flex w-100">
    <div class="flex-grow-1">
      <div>Add condition items to the vehicle by clicking on the vehicle image.</div>
      <div>Click on the condition item to edit or delete it.</div>
    </div>
    <div class="flex-shrink-1">
      <i class="fa fa-times"></i>
    </div>
  </div>
</div>

<div class="kipper" (window:resize)="onResize($event)">
  <canvas #canvas (click)="kipperClicked($event)"
          (mousemove)="pick($event)" style="z-index: 0; width: 100%; height: 100%; display: block;"></canvas>
  <div *ngFor="let point of points" class="point-container">
    <div
      [class]="point.item?.appraisalMedia?.length > 0 ? 'point-with-photo point-loc' : 'point point-loc'"
      [style.left.%]="point.x" [style.top.%]="point.y"
      (click)="editItem(point.item)">

      <span *ngIf="point.item && point.item.damage"
            mdbTooltip="{{point.item.damage.damageDescription
            + ' '
            + (point.item.damageSeverity?.damageSeverityDescription || '')
            + '\n'
            + (point.item.damageDetail?.detailDescription || '')}}">{{ point.text }}</span>
    </div>
  </div>
</div>
<div class="bodyPart mt-2">{{ selectedBodyPart?.partName }}</div>

<div>
  <table class="table table-striped table-sm table-hover appraisal-table">
    <thead>
    <tr>
      <th>&nbsp;</th>
      <th>&nbsp;</th>
      <th>Location</th>
      <th>Damage</th>
      <th>Severity</th>
      <th>&nbsp;</th>
    </tr>
    </thead>
    <tbody>

    <tr *ngFor="let item of appraisal?.appraisalItems; let i = index;" (click)="editItem(item)"
        [ngClass]="(item?.appraisalMedia && item?.appraisalMedia?.length > 0) ? 'have-photos' : ''">
      <td nowrap>
                  <span *ngIf="item.appraisalMedia?.length > 0">
                    <i class="fa fa-camera ml-2"></i>
                    <span *ngIf="item.appraisalMedia?.length > 1" class="photo-count">
                      x {{ item.appraisalMedia.length }}
                    </span>
                  </span>
      </td>
      <td>
        <span
          [class]="item.appraisalMedia?.length > 0 ? 'point-with-photo-text' : 'point-text'">{{ item.splatDesc }}</span>
      </td>
      <td>{{ item.bodyPart?.partName }}</td>
      <td>{{ item.damage?.damageDescription }}</td>
      <td>{{ item.damageSeverity?.damageSeverityDescription }}</td>
      <td *ngIf="!readOnly" nowrap class="icon-cell">
        <i class="fa fa-times-circle mr-2 link-style link-style-danger"
           (click)="deleteAppraisalItem($event, item)"></i>
      </td>
    </tr>
    </tbody>
  </table>
</div>

<div mdbModal #appraisalModal="mdbModal" class="modal fade" tabindex="-1"
     role="dialog" aria-labelledby="appraisalModalLabel" aria-hidden="true"
     [config]="{backdrop: 'static', keyboard: false}">

  <div *ngIf="form && selectedBodyPart" class="modal-dialog-centered">

    <div class="modal-dialog modal-width" role="document">

      <form [formGroup]="form" (ngSubmit)="submitForm()">

        <div class="modal-content mb-5">

          <div class="modal-header">
            {{ selectedBodyPart?.partName }} - ({{ selectedBodyPartGroup?.groupName }} )
          </div>

          <div class="modal-body">

            <div class="d-flex center-box pb-3">
              <div class="flex-grow-1">

                <span *ngIf="newItemStep > 1 && f.damageId.value">
                  <button *ngIf="newItemStep > 1" class="btn btn-sm btn-secondary mr-1 mb-2" (click)="unSetItemStep(1)">
                    {{ getDamageSeverityOption(f.damageId.value) }}
                  </button>
                  <button *ngIf="newItemStep > 2 && f?.damageSeverityId?.value"
                          class="btn btn-sm btn-secondary mr-1 mb-2"
                          (click)="unSetItemStep(2)">
                    {{ getDamageSeverityDescription(f?.damageSeverityId?.value) }}
                  </button>

                  <button *ngIf="newItemStep > 3 && f.damageDetailId?.value" class="btn btn-sm btn-secondary mr-1 mb-2"
                          (click)="unSetItemStep(3)">

                    {{ getDetailDescription(f.damageDetailId.value) }}

                  </button>
                </span>

                <div *ngIf="newItemStep >= 1 && newItemStep <= 3" class="mb-2 enter-text clearfix">
                  <div class="damage-intro" *ngIf="newItemStep == 1 && damageOptions?.length > 0">Select condition
                    type
                  </div>
                  <div class="damage-intro" *ngIf="newItemStep == 2 && damageSeverityOptions?.length > 0">Select
                    condition severity
                  </div>
                  <div class="damage-intro" *ngIf="newItemStep == 3 && damageDetailOptions?.length > 0">Select condition
                    detail
                  </div>
                </div>


                <span *ngIf="newItemStep == 1 && damageOptions?.length > 0">
                  <div
                    style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); grid-gap: 10px">

                    <button *ngFor="let option of damageOptions"
                            class="btn btn-sm btn-primary"
                            mdbRadio="{{ option.id }}"
                            formControlName="damageId"
                            [id]="'damageOption' + option.id"
                            mdbWavesEffect
                            (click)="damageSelected(option)"
                    >{{ option.damageDescription }}</button>
                  </div>
                </span>

                <span *ngIf="newItemStep == 2 && damageSeverityOptions?.length > 0">
                  <div
                    style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); grid-gap: 10px">
                    <button
                      *ngFor="let option of damageSeverityOptions"
                      class="btn btn-sm btn-primary"
                      [value]="option.id"
                      mdbRadio="{{ option.id }}"
                      formControlName="damageSeverityId"
                      mdbWavesEffect
                      (click)="damageSeveritySelected(option)">
                      {{ option.damageSeverityDescription }}
                    </button>
                  </div>
                </span>

                <span *ngIf="newItemStep == 3 && damageDetailOptions?.length > 0">
                   <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); grid-gap: 10px">
                      <div *ngFor="let option of damageDetailOptions"
                             class="btn btn-sm btn-primary mr-1"
                             [value]="option.id"
                             mdbRadio="{{ option.id }}"
                             formControlName="damageDetailId"
                             mdbWavesEffect
                             (click)="damageDetailSelected(option)">
                        {{ option.detailDescription }}</div>
                    </div>
                  </span>
              </div>

            </div>

            <div class="md-form" *ngIf="f?.damageId?.value">
              <input mdbInput class="float-label form-control" id="itemDescription" type="text"
                     formControlName="itemDesc"/>
              <label for="itemDescription">Comments</label>
            </div>

            <div *ngIf="f['damageId'].value">

              <app-dragdropfiles [filesUploading]="filesUploading" (fileDropped)="onFileDropped($event, 'Image')"
                                 (dragDropClosed)="dragDropClosed()"></app-dragdropfiles>

              <div>

                <div *ngFor="let image of unHiddenImages(imageList)"
                     style="height: 70px; display: inline-block; aspect-ratio: var(--aspectRatio, 1.333); margin-right: 5px; margin-bottom: 5px;">

                  <div class="vehicle-image" style="height: 100%; width: 100%; position: relative;">

                    <div *ngIf="image.statusId == statusEnum.Pending" class="text-center">

                      <div class="pt-4">
                        <i class="fa fa-spinner fa-pulse fa-2x"></i>
                        <br><span style="font-size: 12px;">Loading..</span>
                      </div>

                    </div>

                    <div *ngIf="image.statusId == statusEnum.Active"
                         class="image-block" (click)="cropMedia($event, image)"
                         style="background-image: url({{ image.url }}); width: 100%; height: 100%; background-size: cover; background-position: center center">
                    </div>

                    <div *ngIf="image.statusId == statusEnum.Active"
                         class="menu-menu" (click)="deleteAppraisalMedia($event,image)">
                      <i class="fa fa-trash-alt"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <div class="text-right">
              <div *ngIf="!readOnly">
                <button class="btn btn-tertiary-outline ml-2" type="button" (click)="cancelAppraisal()">Cancel</button>
                <button [disabled]="(! f['damageId'].value) || savingForm" class="btn btn-tertiary ml-2"
                        style="min-width: 80px;"
                        type="submit">
                  <span *ngIf="savingForm"><i class="fa fa-spin fa-spinner"></i></span>
                  <span *ngIf="!savingForm">Save</span>
                </button>
              </div>
              <div *ngIf="readOnly">
                <button class="btn btn-tertiary ml-2" type="button" (click)="cancelAppraisal()">Close</button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<app-delete-confirm-modal
  [showDialog]="showDeleteConfirm"
  (confirmDelete)="confirmDelete()"
  (modalClosed)="showDeleteConfirm = false"
  [bodyText]="'Are you sure you want to permanently delete this appraisal item?'"
  (cancelDelete)="cancelDelete()"></app-delete-confirm-modal>
