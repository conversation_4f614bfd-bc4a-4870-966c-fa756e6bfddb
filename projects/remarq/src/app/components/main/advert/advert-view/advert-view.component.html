<div class="container-xxl mt-2" [class.ghost]="!advert">

  <p style="scroll-margin-top: 200px; margin-bottom: 1px;" id="page-container"></p>

  <div class="page-top" id="page-top">

    <div class="d-flex flex-nowrap mt-2">

      <div class="flex-grow-1">
        <h1 class="page-header ghost-waiting">

          {{ advert?.makeName }}
          {{ advert?.modelName }}

          <span class="ml-md-2 saletype-label sale-type-{{ advert?.saleTypeId }}-outline"> {{ saleTypeName }}</span>
        </h1>
        <h2 class="sub-header ghost-waiting">
          {{ advert?.engineCc | enginecc }}
          {{ advert?.derivName }}
          {{ advert?.bodyTypeName }}
        </h2>
      </div>


      <div class="flex-shrink-1 text-right d-flex align-items-center" style="grid-gap: 10px;">

        <div>
          <button class="btn btn-secondary btn-xs" mdbTooltip="Edit Advert"
                  *ngIf="user && user.isAdmin"
                  (click)="url.advertEdit(advert?.id)"><i class="fas fa-edit"></i>
          </button>
        </div>

        <div>
          <button class="btn btn-secondary btn-xs" mdbTooltip="Views" *ngIf="user && user.isAdmin"
                  (click)="showViews()"><i class="fas fa-clipboard"></i>
          </button>
        </div>
        <div>
          <button class="btn btn-secondary btn-xs" mdbTooltip="Brokerage" *ngIf="user && user.isAdmin"
                  (click)="showViews()"><i class="fas fa-handshake"></i>
          </button>
        </div>

      </div>
    </div>
  </div>
</div>


<div>
  <div class="container-xxl">
    <div class="pt-2 indent-on-mobile">
      <button class="btn btn-xs btn-primary-outline" appbackbutton><i class="fa fa-angle-left"></i> Previous Page
      </button>
    </div>
    <div class="row pt-3 align-items-start">

      <div class="col-md-7 col-lg-6 col-xl-6 col-xxl-7 order-md-1 order-2 main-gallery" #gallerySide>
        <div style="display: flex; flex-direction: column; grid-gap: 10px;">
          <div class="flex-grow-1" style="overflow: hidden; border-radius: var(--widgetBorderRadius);">
            <app-advert-view-gallery [advertId]="advertId"></app-advert-view-gallery>
          </div>
          <div class="key-infos flex-shrink-1 indent-on-mobile widget widget-padding">
            <div class="key-info key-vrm" *ngIf="advert?.vrm">
              {{ advert?.vrm }}
            </div>

            <div class="key-info">
              <img src="/assets/images/svg/speedometer-icon.svg" style="height: 14px;"> {{ advert?.odometer | number }}
              mi
            </div>

            <div class="key-info"><i class="fa fa-calendar-alt"></i> {{ advert?.plateName }}</div>
            <div class="key-info"><i class="fa fa-users"></i> {{ advert?.owners }} owners</div>
            <div class="key-info"><img src="/assets/images/svg/transmission-icon.svg"
                                       style="height: 12px;"> {{ advert?.transmissionTypeName }}
            </div>
            <div class="key-info"><i class="fa fa-gas-pump"></i> {{ advert?.fuelTypeName }}</div>
            <div class="key-info"><img src="/assets/images/svg/paintbrush-icon.svg"
                                       style="height: 14px;"> {{ advert?.colour }}
            </div>

            <div class="key-info" *ngIf="advert?.serviceHistoryType !== serviceHistoryType.None && advert?.serviceHistoryType !== serviceHistoryType.Unknown">
              <i class="fa fa-wrench"></i>
              @if (advert?.serviceHistoryType == serviceHistoryType.Yes) {
                Yes
              }
              @else {
                  {{ serviceHistoryType[advert?.serviceHistoryType] }} Service History
              }
            </div>
            <div class="key-info" *ngIf="advert?.doors > 0"><img src="/assets/images/svg/car-door-icon.svg"
                                                                 style="height: 12px; margin-bottom: 2px;"> {{ advert?.doors }}
              doors
            </div>
            <div class="key-info" *ngIf="advert?.v5StatusName"><i class="fa fa-book"></i> {{ advert?.v5StatusName }}
            </div>
            <div class="key-info" *ngIf="advert?.vatStatusName "><i
              class="fa fa-file-invoice"></i> {{ advert?.vatStatusName }}
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-5 col-lg-6 col-xl-6 col-xxl-5 order-md-2 order-1">

        <div class="d-flex flex-column h-100">
          <div class="flex-shrink-1">
            <div style="width: 100%; min-height: 100px;">
              <div *ngIf="advert?.runner == false" class="non-runner">
                <i class="fa fa-exclamation-triangle"></i> Non-Runner
              </div>
              <app-advert-bid-box [advertId]="advertId" [user]="user"></app-advert-bid-box>
            </div>
          </div>

          <div class="flex-shrink-1 d-none d-xl-block">
            <ng-container [ngTemplateOutlet]="conditionValuationProvenance"></ng-container>
          </div>

          <div class="flex-grow-1 d-none d-xl-block">
            <div class="d-flex flex-column h-100">
              <ng-container [ngTemplateOutlet]="dealerComment"></ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="d-xl-none d-flex grid-gap-15 flex-wrap w-100">
      <ng-container [ngTemplateOutlet]="conditionValuationProvenance"></ng-container>
    </div>


    <div class="row">
      <div class="col-md-6">


        <div class="widget-header-style indent-on-mobile">Condition</div>

        <div class="widget widget-padding indent-on-mobile" id="xz">

          <div class="d-flex mb-3 grid-gap-15" *ngIf="showInternalExternal || advert?.latestVehicleFaultCheckId">
            <div *ngIf="showInternalExternal">
              <div class="btn-group">
                <label class="btn btn-sm" [class.active]="conditionTab == 1" mdbRadio="1" (click)="setConditionTab(1)">
                  External
                </label>
                <label class="btn btn-sm" [class.active]="conditionTab == 2" mdbRadio="2" (click)="setConditionTab(2)">
                  Internal
                </label>
              </div>
            </div>

            <div *ngIf="advert?.latestVehicleFaultCheckId">
              <app-vehicle-fault-report
                [vehicleFaultCheckId]="advert?.latestVehicleFaultCheckId"></app-vehicle-fault-report>
            </div>

          </div>

          <div *ngIf="conditionTab == 1">
            <app-advert-view-splat (showInternalExternal)="showInternalExternalEvent($event)" [internal]="false"
                                   [advertId]="advertId"></app-advert-view-splat>
          </div>
          <div *ngIf="conditionTab == 2">
            <app-advert-view-splat (showInternalExternal)="showInternalExternalEvent($event)" [advertId]="advertId"
                                   [internal]="true"></app-advert-view-splat>
          </div>
        </div>

        <div class="d-xl-none d-flex flex-column">
          <ng-container [ngTemplateOutlet]="dealerComment"></ng-container>
        </div>

        <div id="advert-view-tyres">
          <app-vehicle-tyres-inline [advertId]="advertId"></app-vehicle-tyres-inline>
        </div>

        <div>
          <app-advert-vehicle-options [advertId]="advertId"></app-advert-vehicle-options>
        </div>

      </div>

      <div class="col-md-6">


        <div class="">
          <app-advert-vehicle-info [advertId]="advertId"></app-advert-vehicle-info>
        </div>


        <div class="" id="advert-view-location">
          <app-advert-location-map [advertId]="advertId"></app-advert-location-map>
        </div>

      </div>
    </div>
  </div>
</div>

<ng-template #dealerComment>
  <div class="widget-header-style indent-on-mobile">Seller Comment</div>
  <div class="widget widget-padding indent-on-mobile" id="sellers-comment-widget">
    {{ advert?.description | decodeHtmlEntities }}
  </div>
</ng-template>

<ng-template #conditionValuationProvenance>

  <div class="d-flex w-100 flex-wrap" style="grid-column-gap: 15px">

    <div class="flex-grow-1 min-flex">
      <app-vehicle-valuation [advertId]="advertId"></app-vehicle-valuation>
    </div>

    <div class="flex-grow-1 min-flex">
      <app-provenance-summary-widget [advertId]="advertId"></app-provenance-summary-widget>
    </div>
  </div>

</ng-template>

<app-fullsize-image-viewer></app-fullsize-image-viewer>
<app-advert-views-information *ngIf="user && user.isAdmin"
                              [advertId]="advertId" [showDialog]="showViewsDialog" (dialogClosed)="viewsDialogClosed()">
</app-advert-views-information>
