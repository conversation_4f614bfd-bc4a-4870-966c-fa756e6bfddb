<div *ngIf="!showEditForm; else templateDetails">

  <form [formGroup]="searchForm">

    <div style="display: grid; grid-gap: 10px; grid-template-columns: repeat(auto-fit, minmax(200px, auto));">

      <div class="select select-sm">

        <mdb-select-2
          [outline]="true"
          style="width: 100%;"
          placeholder="All Template Types"
          formControlName="isWrapper"
          (change)="filterCommsTemplates()">
          <mdb-select-option *ngFor="let xoption of filterIsWrapperChoices"
                             [value]="xoption.value">{{ xoption.label }}
          </mdb-select-option>
        </mdb-select-2>
      </div>

      <div class="select select-sm">
        <mdb-select-2
          [outline]="true"
          style="width: 100%;"
          formControlName="commsEventId"
          placeholder="All Event Codes"
          (change)="filterCommsTemplates()">
          <mdb-select-option *ngFor="let xoption of filterCommsEventChoices"
                             [value]="xoption.value">{{ xoption.label }}
          </mdb-select-option>
        </mdb-select-2>
      </div>

      <div class="select select-sm">
        <mdb-select-2
          [outline]="true"
          style="width: 100%;"
          formControlName="customerId"
          placeholder="All Customers"
          (change)="filterCommsTemplates()">
          <mdb-select-option *ngFor="let xoption of filterCustomerChoices"
                             [value]="xoption.id">{{ xoption.customerName }}
          </mdb-select-option>
        </mdb-select-2>
      </div>

      <div class="d-flex grid-gap-10">
        <div class="flex-grow-1">
          <div class="select select-sm w-100">
            <mdb-select-2
              [outline]="true"
              style="width: 100%"
              formControlName="commsType"
              placeholder="All Comms Types"
              (change)="filterCommsTemplates()">
              <mdb-select-option *ngFor="let xoption of filterCommsTypeChoices"
                                 [value]="xoption.value">{{ xoption.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>
        </div>
        <div class="flex-shrink-1">
          <div class="btn btn-sm btn-tertiary"><i class="fa fa-search"></i></div>
        </div>
      </div>
    </div>
  </form>

  <div class="mt-3 widget">

    <table class="table table-striped table-condensed" mdbTable>
      <thead>
      <tr>
        <th>Type</th>
        <th>Title</th>
        <th>Event Code</th>
        <th>Duplicates</th>
        <th>Wrapped</th>
        <th>Template For</th>
        <th>Comms Type</th>
        <th>MergeModel</th>
        <th>Updated</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let item of commsTemplates" (click)="editTemplate(item.id)">
        <td class="align-middle">
          <div *ngIf="item.isWrapper"> Wrapper</div>
          <div *ngIf="!item.isWrapper"> Content</div>
        </td>
        <td class="align-middle">{{ item.templateName }}</td>
        <td class="align-middle">
          <div *ngIf="item.isWrapper">
            N/A
          </div>
          <div *ngIf="!item.isWrapper">
            <div class="table-line-1">{{ item.commsEvent?.commsEventCode }}</div>
            <div class="table-line-2">{{ item.commsEvent?.title }}</div>
          </div>
        </td>
        <td style="text-align: left" class="align-middle">
          <i class="fa fa-check fa-15x text-success" *ngIf="item.allowDuplicate"></i>
          <i class="fa fa-times fa-15x error-color" *ngIf="!item.allowDuplicate"></i>
        </td>
        <td style="text-align: left" class="align-middle">
          <i class="fa fa-check fa-15x text-success" *ngIf="item.wrapperId > 0"></i>
          <i class="fa fa-times fa-15x error-color" *ngIf="!item.wrapperId"></i>
        </td>
        <td class="align-middle">
          <div>{{
              (item?.customer) ? item.customer.customerName : 'Default Template'
            }}
          </div>
        </td>
        <td class="align-middle">
          <div>{{ CommsTypeName[item.commsType] }}</div>
        </td>
        <td class="align-middle">
          <div *ngIf="item.isWrapper">N/A</div>
          <div *ngIf="!item.isWrapper">{{ MergeModelName[item.commsEvent.mergeModel] }}</div>
        </td>
        <td class="align-middle">
          <div>{{ item.updated | date: "dd/MM/yy HH:mm" }}</div>
        </td>
        <td class="align-middle">
          <div><i class="fa fa-15x fa-times-circle" (click)="deleteTemplate(item.id)"></i></div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>

  <div class="footerPanel">
    <div class="text-right mt-2">
      <button type="button" class="btn btn-xs btn-primary mr-2"
              (click)="addTemplate()">+ Template
      </button>
    </div>
  </div>
</div>

<div #disabledForModal="mdbModal" aria-hidden="true" class="modal fade" mdbModal role="dialog"
     tabindex="-1">

  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close pull-right" aria-label="Close" (click)="disabledForModal.hide()">
          <span aria-hidden="true">×</span>
        </button>
        <div class="modal-title w-100" id="myModalLabel">Include / Exclude Customers from Template</div>
      </div>

      <div class="p-3">

        <div class="row">
          <div class="col-md-6">

            <div style="height: 400px; overflow-y: scroll">
              <table class="table table-striped table-compressed">
                <thead>
                <tr>
                  <th>Included Customers</th>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let item of sortedCustomers(includedCustomers)"
                    class="status-color-{{ item.statusId }}"
                    (dblclick)="moveToExcluded(item)">
                  <td>
                    <div class="table-line-1">{{ item.customerName }}</div>
                    <div class="table-line-2">{{ item.id }}</div>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>

          </div>
          <div class="col-md-6">

            <div style="height: 400px; overflow-y: scroll">
              <table class="table table-striped table-compressed">

                <thead>
                <tr>
                  <th>Excluded Customers</th>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let item of sortedCustomers(excludedCustomers)"
                    class="status-color-{{ item.statusId }}"
                    (dblclick)="moveToIncluded(item)">
                  <td>
                    <div class="table-line-1">{{ item.customerName }}</div>
                    <div class="table-line-2">{{ item.id }}</div>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>

          </div>
        </div>
        <div class="text-right">
          <button type="button" class="btn btn-tertiary" (click)="disabledForModal.hide()">Close</button>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #templateDetails>
  <div class="row">
    <div class="col-md-9">
      <h1 class="page-header" style="line-height: 25px;">Template Details <i class="fa fa-spin fa-spinner"
                                                                             *ngIf="loadingTemplate"></i></h1>
      <div class="widget padding mt-2">

        <form [formGroup]="formGroup" class="mt-1">

          <input type="hidden" formControlName="id" id="id">

          <div class="d-flex flex-wrap gap-10">


            <div class="flex-grow-1">
              <div class="input-label">Template Name</div>
              <div class="md-form input-sm">
                <input mdbInput id="templateName" formControlName="templateName" class="form-control">
              </div>
            </div>

            <div class="flex-grow-1">
              <div class="input-label">Event Code</div>
              <div class="select select-sm">
                <mdb-select-2
                  [outline]="true"
                  style="width: 100%;"
                  placeholder="Event Code"
                  formControlName="commsEventId">
                  <mdb-select-option *ngFor="let xoption of commsEventChoices"
                                     [value]="xoption.value">{{ xoption.label }}
                  </mdb-select-option>
                </mdb-select-2>
              </div>
            </div>

            <div class="flex-shrink-1">
              <div class="py-4">
                <mdb-checkbox formControlName="isWrapper" id="isWrapper">
                  <label for="isWrapper">Is a wrapper</label>
                </mdb-checkbox>
              </div>
            </div>
          </div>

          <div class="d-flex flex-wrap gap-10">


            <div class="flex-grow-1">

              <div class="input-label">Template Used By</div>
              <div class="select select-sm">
                <mdb-select-2
                  [outline]="true"
                  style="width: 100%;"
                  placeholder="Template For"
                  formControlName="customerId"
                >
                  <mdb-select-option *ngFor="let xoption of customerChoices"
                                     [value]="xoption.id">{{ xoption.customerName }}
                  </mdb-select-option>
                </mdb-select-2>
              </div>
            </div>

            <div class="flex-shrink-1">
              <div *ngIf="f.customerId == 'null'" class="py-3">
                <button class="btn btn-sm btn-secondary" (click)="disabledForModal.show()">Disabled
                  for {{ excludedCount }}
                  Customers
                </button>
              </div>
            </div>
          </div>


          <div class="d-flex flex-wrap gap-10 mt-1">

            <div class="flex-grow-1">

              <div class="input-label">Status</div>
              <div class="select select-sm">
                <mdb-select-2
                  [outline]="true"
                  style="width: 100%;"
                  placeholder="Status"
                  formControlName="statusId">
                  <mdb-select-option *ngFor="let xoption of statusOptions"
                                     [value]="xoption.value">{{ xoption.label }}
                  </mdb-select-option>
                </mdb-select-2>
              </div>
            </div>

            <div class="flex-grow-1">
              <div class="input-label">{{ uniquePlaceHolder(f.commsEventId, f.uniqueIdTemplate) }}</div>
              <div class="md-form input-sm" style="margin-bottom: 0px !important;">
                <input mdbInput id="uniqueIdTemplate" formControlName="uniqueIdTemplate"
                       [placeholder]="uniquePlaceHolder(f.commsEventId, f.uniqueTemplate)"
                       class="form-control">
              </div>
            </div>

            <div class="flex-grow-1">
              <div class="py-4">
                <mdb-checkbox formControlName="allowDuplicate"><label>Allow Duplicates</label></mdb-checkbox>
              </div>
            </div>
          </div>

          <div class="d-flex flex-wrap gap-10">
            <div class="flex-grow-1">
              <div class="input-label">Delivery Method</div>
              <div class="select select-sm">
                <mdb-select-2
                  [outline]="true"
                  style="width: 100%;"
                  formControlName="commsType">
                  <mdb-select-option *ngFor="let xoption of commsTypeChoices"

                                     [value]="xoption.value">{{ xoption.label }}
                  </mdb-select-option>
                </mdb-select-2>
              </div>
            </div>

            <div class="flex-grow-1" *ngIf="f.commsType == 2">
              <div class="md-form small-input">
                <input mdbInput id="senderName" formControlName="senderName" class="form-control float-label">
                <label for="senderName">SMS ID (Max 11 chars)</label>
              </div>
            </div>

            <div class="flex-grow-1" *ngIf="!f.isWrapper">

              <div class="input-label">Wrapper</div>

              <div class="select select-sm">
                <mdb-select-2
                  [outline]="true"
                  style="width: 100%"
                  placeholder="Wrapper"
                  formControlName="wrapperId"
                >
                  <mdb-select-option *ngFor="let xoption of wrapperChoices"
                                     [value]="xoption.value">{{ xoption.label }}
                  </mdb-select-option>
                </mdb-select-2>
              </div>
            </div>
            <div class="flex-shrink-1" *ngIf="f.wrapperId > 0" style="padding-top: 16px;">
              <button (click)="editTemplate(f.wrapperId)" class="btn btn-sm btn-secondary">Edit Wrapper</button>
            </div>
          </div>

          <div class="d-flex flex-wrap mt-3 gap-10">

            <div *ngIf="!f.isWrapper" class="flex-grow-1">

              <div class="input-label">Recipient Address</div>
              <textarea
                mdbInput
                id="recipientAddress"
                formControlName="recipientAddress" class="form-control md-textarea"
                style="width: 100%; height: 70px;"
              >
            </textarea>
            </div>

          </div>

          <div class="d-flex flex-wrap grid-gap-15 mt-2">
            <div class="flex-grow-1" *ngIf="f.commsType == 1 && ! f.isWrapper">
              <div class="input-label">Subject</div>
              <div>
                <textarea id="subjectTemplate"
                          mdbInput
                          style="width: 100%; height: 70px;"
                          class="form-control md-textarea"
                          placeholder="Enter Subject Content Here"
                          formControlName="subjectTemplate"></textarea>
              </div>
            </div>
            <div class="flex-grow-1" *ngIf="f.commsType == 1 && ! f.isWrapper">
              <div class="input-label">Header/Title</div>
              <div>
                <textarea id="headerTemplate"
                          mdbInput
                          style="width: 100%; height: 70px;"
                          class="form-control md-textarea"
                          placeholder="Enter Header Content Here"
                          formControlName="headerTemplate"></textarea>
              </div>
            </div>
          </div>

          <div class="mt-3 input-label">Image URL</div>
          <div class="md-form input-sm">
            <input mdbInput id="imageUrl" formControlName="imageUrlTemplate" class="form-control">
          </div>

          <div class="mt-3 input-label">Body</div>

          <div>
              <textarea mdbInput (keyup)="checkTemplateSyntax(f.bodyTemplate)" id="bodyTemplate"
                        placeholder="Enter Body Content Here"
                        spellcheck="false"
                        class="form-control md-textarea"
                        style="min-height: 150px; width: 100%;"
                        formControlName="bodyTemplate"></textarea>
            <div *ngIf="bracketDifference < 0" class="error-color"><span ngNonBindable>Opening {{ missing</span></div>
            <div *ngIf="bracketDifference > 0" class="error-color"><span
              ngNonBindable>Closing &#125;&#125; missing</span></div>
          </div>

          <div style="padding: 10px 0; margin-top: 10px;">
            <div class="flex-wrap d-flex gap-10">

              <div class="flex-grow-1">

                <div class="md-form input-sm">
                  <input mdbInput id="previewId" formControlName="previewId" class="form-control float-label">
                  <label for="previewId">Model ID</label>
                </div>
              </div>
              <div class="flex-shrink-1">
                <button (click)="showPreviewModel()" [disabled]="!canPreview()"
                        class="btn btn-primary btn-sm">Show Model
                </button>
              </div>

              <div class="flex-shrink-1">
                <div class="md-form input-sm">
                  <input mdbInput id="previewRecipient" formControlName="previewRecipient"
                         class="form-control float-label">
                  <label for="previewRecipient">Preview Recipient</label>
                </div>
              </div>
            </div>
          </div>

          <div class="text-right mt-2">
            <button class="btn btn-sm btn-primary-outline mr-2" (click)="showEditForm = false">Close</button>

            <button class="btn btn-sm btn-primary-outline mr-2" (click)="preview()" [disabled]="!canPreview()">Preview
              Template
            </button>
            <button class="btn btn-sm btn-primary-outline mr-2" (click)="sendTest()" [disabled]="!canSendTest()">Send
              Test
            </button>
            <button class="btn btn-sm btn-primary" (click)="save()" [disabled]="!canSave()">Save Changes</button>
          </div>
        </form>
      </div>
    </div>

    <div class="col-md-3" style="font-size: 12px;">

      <div style="font-weight: bold; font-size: 13px; line-height: 15px; padding-bottom: 7px;">NOTE: All functions
        must be preceeded by a <b>|</b> symbol, eg. task.CompletedDateTime | modify_hours: 3
      </div>
      <li><strong>modify_days: X</strong> Modify the datetime by X days (can be a positive or negative value)</li>
      <li><strong>modify_hours: X</strong> Modify the datetime by X hours (can be a positive or negative value)
      </li>
      <li><strong>modify_minutes: X</strong> Modify the datetime by X minutes (can be a positive or negative
        value)
      </li>
      <li><strong>plus_time_span: X</strong> Add a timespan (eg. Task.EstimatedTime) to the datetime</li>
      <li><strong>minus_time_span: X</strong> Subtract a timespan (eg. Task.EstimatedTime) to the datetime value
      </li>
      <li><strong>minus_time_span: X</strong> Subtract a timespan (eg. Task.EstimatedTime) to the datetime value
      </li>
      <li><strong>minus_time_span: X</strong> Subtract a timespan (eg. Task.EstimatedTime) to the datetime value
      </li>
      <li><strong>metres_to_miles</strong> Converts the value from metres to miles.</li>
      <li><strong>round: X</strong> Rounds a decimal to X decimal places.</li>

    </div>
  </div>
</ng-template>

<div mdbModal #previewModal="mdbModal" class="modal fade" tabindex="-1" role="dialog"
     aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close pull-right" aria-label="Close" (click)="previewModal.hide()">
          <span aria-hidden="true">×</span>
        </button>
        <div class="modal-title w-100">Model</div>
      </div>
      <div class="modal-body"
           *ngIf="previewModel != null && previewModel.hash != null"
           style="height: 500px; overflow-y: scroll">
        <div style="height: 4000px;">
          <ngx-json-viewer [json]="previewModel?.hash"></ngx-json-viewer>
        </div>
      </div>
    </div>
  </div>
</div>


<div mdbModal #previewResponseModal="mdbModal" class="modal fade" tabindex="-1" role="dialog"
     aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close pull-right" aria-label="Close" (click)="previewResponseModal.hide()">
          <span aria-hidden="true">×</span>
        </button>
        <div class="modal-title w-100">Preview</div>
      </div>
      <div class="modal-body">

        <div class="" *ngIf="previewResponse != null" style="max-height: 500px; overflow-y: scroll">

          <div *ngIf="!previewResponse.success">
            TEMPLATE ERROR
          </div>

          <div *ngIf="previewResponse.recipientAddress">
            Recipient Address
            <div class="preview">{{ previewResponse.recipientAddress }}</div>
          </div>
          <div *ngIf="previewResponse.bccAddress" class="mt-2">
            BCC Address
            <div class="preview">{{ previewResponse.bccAddress }}</div>
          </div>
          <div *ngIf="previewResponse.subject">
            Subject
            <div class="preview">{{ previewResponse.subject }}</div>
          </div>
          <div *ngIf="previewResponse.imageUrl">
            ImageUrl
            <div class="preview">{{ previewResponse.imageUrl }}</div>
          </div>
          <div *ngIf="previewResponse.body" class="mt-2">
            Body

            <span class="display-toggle" (click)="showText=true">Text</span>
            <span class="display-toggle" (click)="showText=false">HTML</span>

            <div *ngIf="showText" class="text-preview preview">{{ previewResponse.body }}</div>
            <div *ngIf="!showText" class="html-preview preview" [innerHTML]="previewResponse.body"></div>
          </div>
          <div class="mt-2">
            Unique Id
            <div class="preview">{{ previewResponse.uniqueId }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #loading>Loading...</ng-template>

