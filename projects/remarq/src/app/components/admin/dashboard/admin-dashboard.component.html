<div class="d-flex mt-3" style="align-items: center">
  <div class="flex-grow-1">
    <mdb-tabset [buttonClass]="''" class="classic-tabs" #tabs>
      <mdb-tab (select)="setTab = 1" heading="Staff Onboarding Stats"></mdb-tab>
      <mdb-tab *ngIf="currentUser && currentUser?.isAdmin" (select)="setTab = 2" heading="Admin Stats"></mdb-tab>

      <!-- KPIs -->
      <mdb-tab (select)="setTab = 3" heading="KPIs"></mdb-tab>

    </mdb-tabset>
  </div>
  <div class="flex-shrink-1">
    <ng-container [ngTemplateOutlet]="adminFilter"></ng-container>
  </div>
</div>

<div class="mt-3">

  <!-- tab1 -->
  <div *ngIf="setTab == 1">
    <app-staff-call-ratio-charts></app-staff-call-ratio-charts>
  </div>

  <!-- tab2 -->
  <div *ngIf="setTab == 2">
    <ng-container [ngTemplateOutlet]="adminStatsTemplate"></ng-container>
  </div>

  <div *ngIf="setTab == 3">
    <app-admin-kpis></app-admin-kpis>
  </div>
</div>

<ng-template #adminFilter>
  <div class="d-flex grid-gap-15">
    <div class="flex-shrink-1">
      <div style="flex-shrink: 1" class="btn btn-sm btn-secondary" (click)="filterToMe()"
           mdbTooltip="Customers assigned to me"><i
        [class]="f.assignedToId == currentUser?.contactId ? 'fas fa-user-tie' : 'fa fa-users'"></i>
      </div>
    </div>

    <div class="flex-shrink-1">
      <div class="select select-sm no-margin" style="min-width: 200px; flex-grow: 1">
        <form [formGroup]="filterForm">
          <mdb-select-2 [outline]="true"
                        formControlName="assignedToId"
                        (valueChange)="loadData($event)"
                        class="white-background"
                        placeholder="Select Assigned">
            <mdb-select-option [value]="EMPTY_GUID">All Assignees</mdb-select-option>
            <mdb-select-option *ngFor="let ato of assignedOptions"
                               [value]="ato.value"
                               class="adminstatus-{{ ato.statusId }}">{{ ato.label }}
            </mdb-select-option>
          </mdb-select-2>
        </form>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #adminStatsTemplate>
  <div class="stats mt-3">
    <div class="stat-cell widget" (click)="adminUrl.adminCustomers(2)">
      <div class="inner ">
      <span class="line1">
        <span *ngIf="adminStats"> {{ adminStats.pendingCustomers }}</span>
        <span *ngIf="!adminStats"><i class="fa fa-spin fa-spinner"></i></span>
      </span>
        <div class="line2">Accounts</div>
        <div class="line3">Pending</div>
      </div>
    </div>
    <div class="stat-cell widget" (click)="adminUrl.adminOffers()">
      <div class="inner">
        <div class="line1">
        <span *ngIf="adminStats">
          {{ adminStats.offersPending }}</span>
          <span *ngIf="!adminStats"><i class="fa fa-spin fa-spinner"></i></span>
        </div>
        <div class="line2">Offers</div>
        <div class="line3">Pending</div>
      </div>
    </div>

    <div class="stat-cell widget" (click)="adminUrl.adminCustomers(1)">
      <div class="inner">
        <div class="line1">
        <span *ngIf="adminStats">
          {{ adminStats.activeCustomers }}</span>
          <span *ngIf="!adminStats"><i class="fa fa-spin fa-spinner"></i></span>
        </div>
        <div class="line2">Customers</div>
        <div class="line3">Active</div>
      </div>
    </div>
    <div class="stat-cell widget" (click)="adminUrl.adminAdverts()">
      <div class="inner">
        <div class="line1">
        <span *ngIf="adminStats">
          {{ adminStats.liveAdverts }}</span>
          <span *ngIf="!adminStats"><i class="fa fa-spin fa-spinner"></i></span>
        </div>
        <div class="line2">Adverts</div>
        <div class="line3">Live</div>
      </div>
    </div>
    <div class="stat-cell widget" (click)="adminUrl.adminDeals(0)">
      <div class="inner">
        <div class="line1">
        <span *ngIf="adminStats">
          {{ adminStats.soldToday }}</span>
          <span *ngIf="!adminStats"><i class="fa fa-spin fa-spinner"></i></span>
        </div>
        <div class="line2">Sold</div>
        <div class="line3">Today</div>
      </div>
    </div>
    <div class="stat-cell widget" (click)="adminUrl.adminDeals(30)">
      <div class="inner">
        <div class="line1">
        <span *ngIf="adminStats">
          {{ adminStats.sold30Days }}</span>
          <span *ngIf="!adminStats"><i class="fa fa-spin fa-spinner"></i></span>
        </div>
        <div class="line2">Sold</div>
        <div class="line3">30 Days</div>
      </div>
    </div>

    <div class="stat-cell widget" (click)="adminUrl.adminInvoices()">
      <div class="inner">
        <div class="line1">
          <span *ngIf="adminStats">{{ adminStats.overdueAccounts }}</span>
          <span *ngIf="!adminStats"><i class="fa fa-spin fa-spinner"></i></span>
        </div>
        <div class="line2">Invoices</div>
        <div class="line3">Overdue</div>
      </div>
    </div>

  </div>

  <div class="row">
    <div class="col-md-6">
      <div class="chart-title">Customer accounts (by month)
        <app-dashboard-trendline-chart [chartData]="activeCustomersData"
                                       colourShades="Blue"></app-dashboard-trendline-chart>
      </div>
    </div>
    <div class="col-md-6">
      <div class="chart-title">Customer accounts (by week)
        <app-dashboard-trendline-chart [chartData]="activeCustomersDataWeekly"
                                       colourShades="Blue"></app-dashboard-trendline-chart>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6">
      <div class="chart-title">Live Listings (by month)
        <app-dashboard-trendline-chart [chartData]="liveListingsData"
                                       colourShades="Green"></app-dashboard-trendline-chart>
      </div>
    </div>
    <div class="col-md-6">
      <div class="chart-title">Live Listings (by week)
        <app-dashboard-trendline-chart [chartData]="liveListingsDataWeekly"
                                       colourShades="Green"></app-dashboard-trendline-chart>
      </div>
    </div>
  </div>

  <div class="chart-title">Sales by Customer (click customer to toggle)
    <app-dashboard-bar-chart [chartData]="salesByCustomerData"></app-dashboard-bar-chart>
  </div>
</ng-template>
