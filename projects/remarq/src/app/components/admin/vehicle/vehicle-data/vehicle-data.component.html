<h1 class="page-header">Vehicle Data Lookup</h1>
<div class="mt-2">
  <div class="d-flex flex-wrap">
    <div class="">
      <div class="card">
        <div class="card-header">
          <p class="card-text">Enter vehicle details and select data sources to retrieve vehicle information.</p>
        </div>
        <div class="card-body">
          <form [formGroup]="vehicleDataForm" (ngSubmit)="onSubmit()">

            <!-- Vehicle Registration -->
            <div class="form-group">
              <label for="registration" class="form-label">
                Vehicle Registration <span class="text-danger">*</span>
              </label>
              <input
                type="text"
                id="registration"
                class="form-control"
                [class.is-invalid]="isFieldInvalid('registration')"
                formControlName="registration"
                style="text-transform: uppercase;"
                placeholder="VRM"
                maxlength="10">
              <div class="invalid-feedback" *ngIf="isFieldInvalid('registration')">
                {{ getFieldError('registration') }}
              </div>
            </div>

            <!-- Mileage -->
            <div class="form-group">
              <label for="mileage" class="form-label">Mileage (Optional)</label>
              <input
                type="number"
                id="mileage"
                class="form-control"
                [class.is-invalid]="isFieldInvalid('mileage')"
                formControlName="mileage"
                placeholder="Mileage"
                min="0">
              <div class="invalid-feedback" *ngIf="isFieldInvalid('mileage')">
                {{ getFieldError('mileage') }}
              </div>
            </div>

            <!-- VIN -->
            <div class="form-group">
              <label for="vin" class="form-label">VIN (Optional)</label>
              <input
                type="text"
                id="vin"
                class="form-control"
                formControlName="vin"
                placeholder="VIN"
                maxlength="17">
              <small class="form-text text-muted">
                VIN is used for additional CAP data lookup when available.
              </small>
            </div>

            <!-- Data Source Selection -->
            <div class="form-group">
              <label class="form-label">Data Sources <span class="text-danger">*</span></label>
              <div class="form-check-group">

                <div class="form-check">
                  <input
                    type="checkbox"
                    id="autoTraderMetrics"
                    class="form-check-input"
                    formControlName="autoTraderMetrics">
                  <label class="form-check-label" for="autoTraderMetrics">
                    AutoTrader Metrics
                  </label>
                </div>

                <div class="form-check">
                  <input
                    type="checkbox"
                    id="autoTraderFeatures"
                    class="form-check-input"
                    formControlName="autoTraderFeatures">
                  <label class="form-check-label" for="autoTraderFeatures">
                    AutoTrader Features
                  </label>
                </div>

                <div class="form-check">
                  <input
                    type="checkbox"
                    id="autoTraderValuation"
                    class="form-check-input"
                    formControlName="autoTraderValuation">
                  <label class="form-check-label" for="autoTraderValuation">
                    AutoTrader Valuation
                  </label>
                </div>

                <div class="form-check">
                  <input
                    type="checkbox"
                    id="capData"
                    class="form-check-input"
                    formControlName="capData">
                  <label class="form-check-label" for="capData">
                    CAP Data
                  </label>
                </div>

              </div>
              <small class="form-text text-muted">
                Select at least one data source to retrieve information.
              </small>
            </div>

            <!-- Submit Button -->
            <div class="form-group">
              <button
                type="submit"
                class="btn btn-primary"
                [disabled]="isLoading">
                <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
                {{ isLoading ? 'Fetching Data...' : 'Submit' }}
              </button>
              <button
                type="button"
                class="btn btn-secondary ms-2"
                (click)="resetForm()"
                [disabled]="isLoading">
                Reset
              </button>
            </div>

          </form>
        </div>
      </div>
    </div>
    <div class="" *ngIf="hasSubmitted && vehicleDataResponse">
      <div class="card">
        <div class="card-header">
          <h4 class="card-title">Vehicle Data Results</h4>
        </div>
        <div class="card-body vehicle-data-results">

          <!-- Errors -->
          <div *ngIf="vehicleDataResponse.errors && vehicleDataResponse.errors.length > 0" class="alert alert-warning">
            <h6>Errors encountered:</h6>
            <ul class="mb-0">
              <li *ngFor="let error of vehicleDataResponse.errors">{{ error }}</li>
            </ul>
          </div>

          <!-- AutoTrader Lookup Data b-->
          <div *ngIf="vehicleDataResponse.autoTraderData" class="mb-4">
            <h5>AutoTrader Data</h5>
            <div class="card">
              <div class="card-body">
                <pre class="bg-light p-3">{{ vehicleDataResponse.autoTraderData | json }}</pre>
              </div>
            </div>
          </div>

          <!-- AutoTrader Metrics -->
          <div *ngIf="vehicleDataResponse.autoTraderMetrics" class="mb-4">
            <h5>AutoTrader Metrics</h5>
            <div class="card">
              <div class="card-body">
                <pre class="bg-light p-3">{{ vehicleDataResponse.autoTraderMetrics | json }}</pre>
              </div>
            </div>
          </div>

          <!-- AutoTrader Features -->
          <div *ngIf="vehicleDataResponse.autoTraderFeatures" class="mb-4">
            <h5>AutoTrader Features</h5>
            <div class="card">
              <div class="card-body">
                <pre class="bg-light p-3">{{ vehicleDataResponse.autoTraderFeatures | json }}</pre>
              </div>
            </div>
          </div>

          <!-- AutoTrader Valuation -->
          <div *ngIf="vehicleDataResponse.autoTraderValuation" class="mb-4">
            <h5>AutoTrader Valuation</h5>
            <div class="card">
              <div class="card-body">
                <pre class="bg-light p-3">{{ vehicleDataResponse.autoTraderValuation | json }}</pre>
              </div>
            </div>
          </div>


          <!-- CAP Data -->
          <div *ngIf="vehicleDataResponse.capData" class="mb-4">
            <h5>CAP Data</h5>
            <div class="card">
              <div class="card-body">
                <pre class="bg-light p-3">{{ vehicleDataResponse.capData | json }}</pre>
              </div>
            </div>
          </div>

          <!-- CAP VIN Data -->
          <div *ngIf="vehicleDataResponse.capVinData" class="mb-4">
            <h5>CAP VIN Data</h5>
            <div class="card">
              <div class="card-body">
                <pre class="bg-light p-3">{{ vehicleDataResponse.capVinData | json }}</pre>
              </div>
            </div>
          </div>

          <!-- No Data Message -->
          <div *ngIf="!vehicleDataResponse.autoTraderValuation &&
                      !vehicleDataResponse.autoTraderMetrics &&
                      !vehicleDataResponse.autoTraderFeatures &&
                      !vehicleDataResponse.capData &&
                      !vehicleDataResponse.capVinData &&
                      (!vehicleDataResponse.errors || vehicleDataResponse.errors.length === 0)"
               class="alert alert-info">
            No data was returned from the selected sources.
          </div>

        </div>
      </div>
    </div>
  </div>

</div>
