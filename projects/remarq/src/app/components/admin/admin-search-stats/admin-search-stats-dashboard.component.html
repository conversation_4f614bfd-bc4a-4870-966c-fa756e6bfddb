<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h2 class="h4 mb-0">Search Analytics Dashboard</h2>
        <button
          class="btn btn-primary btn-sm"
          (click)="refresh()"
          [disabled]="loading">
          <i class="fas fa-sync-alt" [class.fa-spin]="loading"></i>
          Refresh
        </button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading && !overview" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="alert alert-danger" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    {{ error }}
  </div>

  <!-- Dashboard Cards -->
  <div *ngIf="overview" class="row">
    <!-- Total Customers -->
    <div class="col-lg-3 col-md-6 mb-4">
      <div class="card border-left-primary shadow h-100">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                Total Customers
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ overview.totalCustomers | number }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-users fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Customers with Saved Searches -->
    <div class="col-lg-3 col-md-6 mb-4">
      <div class="card border-left-success shadow h-100">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                Customers With No Alerts
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ overview.customersWithNoSearches | number }}
              </div>
            </div>
            <div class="col-auto cursor-pointer search-icon-container" (click)="viewCustomersWithNoAlerts()">
              <i class="fas fa-search fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Saved Searches -->
    <div class="col-lg-3 col-md-6 mb-4">
      <div class="card border-left-info shadow h-100">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                Total Saved Searches
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ overview.totalSavedSearches | number }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-bookmark fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Customer Engagement Rate -->
    <div class="col-lg-3 col-md-6 mb-4">
      <div class="card border-left-warning shadow h-100">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                Engagement Rate
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ overview.customerEngagementRate | number:'1.1-1' }}%
              </div>
              <div class="progress progress-sm mt-2">
                <div
                  class="progress-bar bg-warning"
                  role="progressbar"
                  [style.width.%]="overview.customerEngagementRate">
                </div>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-chart-line fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Active Saved Searches -->
    <div class="col-lg-3 col-md-6 mb-4">
      <div class="card border-left-secondary shadow h-100">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                Active Searches
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ overview.activeSavedSearches | number }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-bell fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Active Adverts -->
    <div class="col-lg-3 col-md-6 mb-4">
      <div class="card border-left-primary shadow h-100">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                Active Adverts
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ overview.totalActiveAdverts | number }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-car fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Adverts Notified About -->
    <div class="col-lg-3 col-md-6 mb-4">
      <div class="card border-left-success shadow h-100">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                Adverts Notified
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                {{ overview.totalAdvertsNotifiedAbout | number }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-envelope fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- System Health Status -->
    <div class="col-lg-3 col-md-6 mb-4">
      <div class="card shadow h-100">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-uppercase mb-1">
                System Health
              </div>
              <div class="h5 mb-0 font-weight-bold" [ngClass]="getHealthStatusClass(overview.systemHealthStatus)">
                <i class="fas fa-circle me-2"></i>
                {{ overview.systemHealthStatus }}
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-heart fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Key Metrics Table -->
  <div *ngIf="overview?.keyMetrics?.length" class="row mt-4">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header">
          <h6 class="m-0 font-weight-bold text-primary">Key System Metrics</h6>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
              <tr>
                <th>Metric</th>
                <th>Value</th>
                <th>Description</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let metric of overview.keyMetrics">
                <td class="font-weight-bold">{{ metric.name }}</td>
                <td>{{ metric.value }}</td>
                <td class="text-muted">{{ metric.description }}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Customers with No Alerts Modal -->
<div mdbModal #customersModal="mdbModal" class="modal fade"
     role="dialog" aria-labelledby="customersModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content" style="height: 80vh;">
      <div class="modal-header">
        <h4 class="modal-title" id="customersModalLabel">Customers with No Alerts</h4>
        <button type="button" class="close" (click)="customersModal.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="overflow-y: auto; max-height: calc(80vh - 120px);">
        <div *ngIf="customersWithoutAlerts.length === 0" class="text-center py-3">
          <p class="text-muted">No customers found without alerts.</p>
        </div>
        <div *ngIf="customersWithoutAlerts.length > 0" class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="thead-light" style="position: sticky; top: 0; z-index: 10;">
              <tr>
                <th>Customer Name</th>
                <th>Type</th>
                <th>Live Adverts</th>
                <th>Total Adverts</th>
                <th>Last Login</th>
                <th>Last Advert</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let customer of customersWithoutAlerts"
                  (click)="navigateToCustomer(customer.id)"
                  style="cursor: pointer;"
                  class="clickable-row">
                <td>{{ customer.customerName }}</td>
                <td>
                  <span *ngIf="customer.isBuyer && customer.isSeller" class="badge badge-info">Buyer/Seller</span>
                  <span *ngIf="customer.isBuyer && !customer.isSeller" class="badge badge-success">Buyer</span>
                  <span *ngIf="!customer.isBuyer && customer.isSeller" class="badge badge-warning">Seller</span>
                  <span *ngIf="!customer.isBuyer && !customer.isSeller" class="badge badge-secondary">-</span>
                </td>
                <td>{{ customer.liveAdverts || 0 }}</td>
                <td>{{ customer.totalAdverts || 0 }}</td>
                <td>{{ customer.lastLogin | date:'dd/MM/yy HH:mm' || '-' }}</td>
                <td>{{ customer.lastAdvert | date:'dd/MM/yy HH:mm' || '-' }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="customersModal.hide()">Close</button>
      </div>
    </div>
  </div>
</div>
