<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h2 class="h4 mb-0">Customer Search Analytics</h2>
        <button
          class="btn btn-success btn-sm"
          (click)="exportToCSV()"
          [disabled]="exporting || loading">
          <i class="fas fa-file-csv me-2" [class.fa-spin]="exporting"></i>
          Export CSV
        </button>
      </div>
    </div>
  </div>

  <!-- Filters Card -->
  <div class="card shadow mb-4">
    <div class="card-header">
      <h6 class="m-0 font-weight-bold text-primary">
        <i class="fas fa-filter me-2"></i>
        Filters
      </h6>
    </div>
    <div class="card-body">
      <div class="row">
        <!-- Date Range -->
        <div class="col-lg-3 col-md-6 mb-3">
          <label class="form-label">From Date</label>
          <input
            type="date"
            class="form-control form-control-sm"
            [(ngModel)]="fromDate"
            (ngModelChange)="onFilterChanged()">
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
          <label class="form-label">To Date</label>
          <input
            type="date"
            class="form-control form-control-sm"
            [(ngModel)]="toDate"
            (ngModelChange)="onFilterChanged()">
        </div>

        <!-- Customer Search -->
        <div class="col-lg-3 col-md-6 mb-3">
          <label class="form-label">Customer Name</label>
          <input
            type="text"
            class="form-control form-control-sm"
            placeholder="Search by name..."
            [(ngModel)]="customerNameFilter"
            (ngModelChange)="onFilterChanged()">
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
          <label class="form-label">Customer Email</label>
          <input
            type="text"
            class="form-control form-control-sm"
            placeholder="Search by email..."
            [(ngModel)]="customerEmailFilter"
            (ngModelChange)="onFilterChanged()">
        </div>

        <!-- Assigned To -->
        <div class="col-lg-3 col-md-6 mb-3">
          <label class="form-label">Assigned To</label>
          <input
            type="text"
            class="form-control form-control-sm"
            placeholder="Assigned to..."
            [(ngModel)]="assignedToFilter"
            (ngModelChange)="onFilterChanged()">
        </div>

        <!-- Effectiveness Rate Range -->
        <div class="col-lg-3 col-md-6 mb-3">
          <label class="form-label">Min Effectiveness %</label>
          <input
            type="number"
            class="form-control form-control-sm"
            placeholder="0"
            min="0"
            max="100"
            [(ngModel)]="minEffectivenessRate"
            (ngModelChange)="onFilterChanged()">
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
          <label class="form-label">Max Effectiveness %</label>
          <input
            type="number"
            class="form-control form-control-sm"
            placeholder="100"
            min="0"
            max="100"
            [(ngModel)]="maxEffectivenessRate"
            (ngModelChange)="onFilterChanged()">
        </div>

        <!-- Clear Filters Button -->
        <div class="col-lg-3 col-md-6 mb-3 d-flex align-items-end">
          <button
            class="btn btn-outline-secondary btn-sm w-100"
            (click)="clearFilters()">
            <i class="fas fa-times me-2"></i>
            Clear Filters
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="alert alert-danger" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    {{ error }}
  </div>

  <!-- Data Table -->
  <div class="card shadow">
    <div class="card-header">
      <div class="d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">
          Customer Analytics
          <span *ngIf="totalCount > 0" class="badge badge-secondary ms-2">{{ totalCount }}</span>
        </h6>
        <div *ngIf="loading" class="spinner-border spinner-border-sm text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
    </div>

    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-striped table-hover mb-0">
          <thead class="thead-light">
          <tr>
            <th scope="col" class="cursor-pointer" (click)="onSort('customerName')">
              Customer
              <i class="ms-1" [ngClass]="getSortIcon('customerName')"></i>
            </th>
            <th scope="col" class="cursor-pointer" (click)="onSort('assignedTo')">
              Assigned To
              <i class="ms-1" [ngClass]="getSortIcon('assignedTo')"></i>
            </th>
            <th scope="col" class="cursor-pointer" (click)="onSort('totalSavedSearches')">
              Saved Searches
              <i class="ms-1" [ngClass]="getSortIcon('totalSavedSearches')"></i>
            </th>
            <th scope="col" class="cursor-pointer" (click)="onSort('activeNotificationSearches')">
              Active Notifications
              <i class="ms-1" [ngClass]="getSortIcon('activeNotificationSearches')"></i>
            </th>
            <th scope="col" class="cursor-pointer" (click)="onSort('totalMatchingAdverts')">
              Matching Adverts
              <i class="ms-1" [ngClass]="getSortIcon('totalMatchingAdverts')"></i>
            </th>
            <th scope="col" class="cursor-pointer" (click)="onSort('totalAdvertsNotifiedAbout')">
              Notified About
              <i class="ms-1" [ngClass]="getSortIcon('totalAdvertsNotifiedAbout')"></i>
            </th>
            <th scope="col" class="cursor-pointer" (click)="onSort('notificationEffectivenessRate')">
              Effectiveness Rate
              <i class="ms-1" [ngClass]="getSortIcon('notificationEffectivenessRate')"></i>
            </th>
            <th scope="col">Actions</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let customer of customers | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage, totalItems: totalCount }">
            <td>
              <div class="d-flex flex-column">
                <span class="font-weight-bold">{{ customer.customerName }}</span>
                <small class="text-muted">{{ customer.customerEmail }}</small>
              </div>
            </td>
            <td>
                <span *ngIf="customer.assignedTo" class="badge badge-info">
                  {{ customer.assignedTo }}
                </span>
              <span *ngIf="!customer.assignedTo" class="text-muted">Unassigned</span>
            </td>
            <td>
              <span class="badge badge-primary">{{ customer.totalSavedSearches }}</span>
            </td>
            <td>
              <span class="badge badge-success">{{ customer.activeNotificationSearches }}</span>
            </td>
            <td>{{ customer.totalMatchingAdverts | number }}</td>
            <td>{{ customer.totalAdvertsNotifiedAbout | number }}</td>
            <td>
                <span
                  class="badge"
                  [ngClass]="getEffectivenessBadgeClass(customer.notificationEffectivenessRate)">
                  {{ customer.notificationEffectivenessRate | number:'1.1-1' }}%
                </span>
              <div class="small text-muted mt-1">
                {{ getEffectivenessRating(customer.notificationEffectivenessRate) }}
              </div>
            </td>
            <td>
              <button
                class="btn btn-outline-primary btn-sm"
                (click)="showCustomerDetails(customer)">
                <i class="fas fa-eye me-1"></i>
                Details
              </button>
            </td>
          </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div *ngIf="!loading && customers.length === 0" class="text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No customers found</h5>
        <p class="text-muted">Try adjusting your filters or search criteria.</p>
      </div>
    </div>

    <!-- Pagination -->
    <div class="card-footer" *ngIf="totalCount > 0">
      <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
          Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to
          {{ (currentPage * itemsPerPage) < totalCount ? (currentPage * itemsPerPage) : totalCount }} of
          {{ totalCount }} customers
        </div>
        <pagination-controls
          (pageChange)="onPageChanged($event)"
          [maxSize]="5"
          [directionLinks]="true"
          [autoHide]="true"
          previousLabel="Previous"
          nextLabel="Next">
        </pagination-controls>
      </div>
    </div>
  </div>
</div>

<!-- Customer Details Modal -->
<div class="modal fade" [class.show]="showModal" [style.display]="showModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" (click)="hideModal()">
  <div class="modal-dialog modal-lg" role="document" (click)="$event.stopPropagation()">
    <div class="modal-content" *ngIf="selectedCustomer">
      <div class="modal-header">
        <h5 class="modal-title">
          Customer Details: {{ selectedCustomer.customerName }}
        </h5>
        <button type="button" class="close" (click)="hideModal()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- Customer Summary Cards -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-primary">{{ selectedCustomer.totalSavedSearches }}</h4>
                <p class="card-text">Saved Searches</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-success">{{ selectedCustomer.activeNotificationSearches }}</h4>
                <p class="card-text">Active Notifications</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-info">{{ selectedCustomer.totalMatchingAdverts }}</h4>
                <p class="card-text">Total Matches</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 [ngClass]="getEffectivenessBadgeClass(selectedCustomer.notificationEffectivenessRate)">
                  {{ selectedCustomer.notificationEffectivenessRate | number:'1.1-1' }}%
                </h4>
                <p class="card-text">Effectiveness</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Saved Searches Breakdown -->
        <h6 class="font-weight-bold mb-3">Saved Searches Breakdown</h6>
        <div class="table-responsive">
          <table class="table table-sm table-striped">
            <thead>
            <tr>
              <th>Search Criteria</th>
              <th>Notifications</th>
              <th>Matches</th>
              <th>Notified</th>
              <th>Rate</th>
              <th>Last Notification</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let search of selectedCustomer.savedSearches">
              <td>
                <small>{{ search.searchCriteria }}</small>
              </td>
              <td>
                  <span class="badge" [class.badge-success]="search.notificationsEnabled"
                        [class.badge-secondary]="!search.notificationsEnabled">
                    {{ search.notificationsEnabled ? 'On' : 'Off' }}
                  </span>
              </td>
              <td>{{ search.totalMatchingAdverts }}</td>
              <td>{{ search.totalAdvertsNotifiedAbout }}</td>
              <td>
                  <span class="badge" [ngClass]="getEffectivenessBadgeClass(search.notificationEffectivenessRate)">
                    {{ search.notificationEffectivenessRate | number:'1.1-1' }}%
                  </span>
              </td>
              <td>
                <small class="text-muted">
                  {{ search.lastNotificationSent | date:'short' || 'Never' }}
                </small>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="hideModal()">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Backdrop -->
<div class="modal-backdrop fade" [class.show]="showModal" *ngIf="showModal"></div>
