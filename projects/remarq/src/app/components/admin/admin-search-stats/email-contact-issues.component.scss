// Email Contact Issues Component Styles

.cursor-pointer {
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: #f8f9fc;
  }
}

.table {
  th, td {
    vertical-align: middle;
  }

  th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
  }

  td {
    font-size: 0.875rem;
  }
}

.card {
  border: none;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);

  .card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
  }
}

.badge {
  font-size: 0.75rem;
  font-weight: 600;
}

.border-left-primary {
  border-left: 0.25rem solid #4e73df !important;
}

.border-left-warning {
  border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
  border-left: 0.25rem solid #e74a3b !important;
}

.border-left-info {
  border-left: 0.25rem solid #36b9cc !important;
}

.text-primary {
  color: #4e73df !important;
}

.text-warning {
  color: #f6c23e !important;
}

.text-danger {
  color: #e74a3b !important;
}

.text-info {
  color: #36b9cc !important;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.form-control-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

// Summary cards specific styling
.card-body h4 {
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.card-text.small {
  font-size: 0.75rem;
  margin-bottom: 0;
  color: #6c757d;
}

// Empty state styling
.fa-3x {
  font-size: 3em;
}

// Responsive adjustments
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.8rem;
  }

  .card-body h4 {
    font-size: 1.2rem;
  }
}
