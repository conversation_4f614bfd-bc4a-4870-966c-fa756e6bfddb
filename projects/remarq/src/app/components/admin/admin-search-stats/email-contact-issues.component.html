<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h2 class="h4 mb-0">Email Contact Issues</h2>
        <button
          class="btn btn-success btn-sm"
          (click)="exportToCSV()"
          [disabled]="exporting || loading">
          <i class="fas fa-file-csv me-2" [class.fa-spin]="exporting"></i>
          Export CSV
        </button>
      </div>
    </div>
  </div>

  <!-- Filters Card -->
  <div class="card shadow mb-4">
    <div class="card-header">
      <h6 class="m-0 font-weight-bold text-primary">
        <i class="fas fa-filter me-2"></i>
        Filters
      </h6>
    </div>
    <div class="card-body">
      <div class="row">
        <!-- Date Range -->
        <div class="col-lg-3 col-md-6 mb-3">
          <label class="form-label">From Date</label>
          <input
            type="date"
            class="form-control form-control-sm"
            [value]="fromDate"
            (change)="fromDate = $event.target.value; onFilterChanged()">
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
          <label class="form-label">To Date</label>
          <input
            type="date"
            class="form-control form-control-sm"
            [value]="toDate"
            (change)="toDate = $event.target.value; onFilterChanged()">
        </div>

        <!-- Issue Type Filter -->
        <div class="col-lg-3 col-md-6 mb-3">
          <label class="form-label">Issue Type</label>
          <select
            class="form-control form-control-sm"
            [(ngModel)]="issueTypeFilter"
            (ngModelChange)="onFilterChanged()">
            <option *ngFor="let type of issueTypes" [value]="type.value">
              {{ type.label }}
            </option>
          </select>
        </div>

        <!-- Clear Filters Button -->
        <div class="col-lg-3 col-md-6 mb-3 d-flex align-items-end">
          <button
            class="btn btn-outline-secondary btn-sm w-100"
            (click)="clearFilters()">
            <i class="fas fa-times me-2"></i>
            Clear Filters
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="alert alert-danger" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    {{ error }}
  </div>

  <!-- Data Table -->
  <div class="card shadow">
    <div class="card-header">
      <div class="d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">
          Contact Issues
          <span *ngIf="totalItems > 0" class="badge badge-secondary ms-2">{{ totalItems }}</span>
        </h6>
        <div *ngIf="loading" class="spinner-border spinner-border-sm text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
    </div>

    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-striped table-hover mb-0">
          <thead class="thead-light">
          <tr>
            <th scope="col" class="cursor-pointer" (click)="onSort('email')">
              Email
              <i class="ms-1" [ngClass]="getSortIcon('email')"></i>
            </th>
            <th scope="col" class="cursor-pointer" (click)="onSort('issueType')">
              Issue Type
              <i class="ms-1" [ngClass]="getSortIcon('issueType')"></i>
            </th>
            <th scope="col" class="cursor-pointer" (click)="onSort('issueDate')">
              Issue Date
              <i class="ms-1" [ngClass]="getSortIcon('issueDate')"></i>
            </th>
            <th scope="col" class="cursor-pointer" (click)="onSort('sender')">
              Sender
              <i class="ms-1" [ngClass]="getSortIcon('sender')"></i>
            </th>
            <th scope="col">Description</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let issue of contactIssues | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage, totalItems: totalItems }">
            <td>
              <span class="font-weight-bold">{{ issue.email }}</span>
            </td>
            <td>
              <span class="badge" [ngClass]="getIssueTypeBadgeClass(issue.issueType)">
                {{ issue.issueType }}
              </span>
            </td>
            <td>
              {{ issue.issueDate | date:'short' }}
            </td>
            <td>
              <span class="text-muted">{{ issue.sender }}</span>
            </td>
            <td>
              <small class="text-muted">{{ issue.issueDescription }}</small>
            </td>
          </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div *ngIf="!loading && contactIssues.length === 0" class="text-center py-5">
        <i class="fas fa-envelope-open fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No contact issues found</h5>
        <p class="text-muted">Try adjusting your filters or date range.</p>
      </div>
    </div>

    <!-- Pagination -->
    <div class="card-footer" *ngIf="totalItems > 0">
      <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
          Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to
          {{ (currentPage * itemsPerPage) < totalItems ? (currentPage * itemsPerPage) : totalItems }} of
          {{ totalItems }} contact issues
        </div>
        <div>
          Items per Page:
          <select (change)="handlePageSizeChange($event)" class="form-control form-control-sm d-inline-block w-auto">
            <option *ngFor="let size of pageSizes" [ngValue]="size" [selected]="size === itemsPerPage">
              {{ size }}
            </option>
          </select>
        </div>
        <pagination-controls
          class="paginator"
          previousLabel="Prev"
          nextLabel="Next"
          (pageChange)="onPageChanged($event)">
        </pagination-controls>
      </div>
    </div>
  </div>
</div>
