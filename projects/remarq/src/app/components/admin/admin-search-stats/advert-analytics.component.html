<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h2 class="h4 mb-0">Advert Notifications Analytics</h2>
        <button
          class="btn btn-success btn-sm"
          (click)="exportToCSV()"
          [disabled]="exporting || loading">
          <i class="fas fa-file-csv me-2" [class.fa-spin]="exporting"></i>
          Export CSV
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Advert Details Modal -->
<div class="modal fade" [class.show]="showModal" [style.display]="showModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" (click)="hideModal()">
  <div class="modal-dialog modal-lg" role="document" (click)="$event.stopPropagation()">
    <div class="modal-content" *ngIf="selectedAdvert">
      <div class="modal-header">
        <h5 class="modal-title">
          Advert Details: {{ selectedAdvert.vrm }}
        </h5>
        <button type="button" class="close" (click)="hideModal()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- Advert Summary Cards -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-primary">{{ selectedAdvert.matchedInSavedSearchCount }}</h4>
                <p class="card-text">Saved Search Matches</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-info">{{ selectedAdvert.matchedInUnsavedSearchCount }}</h4>
                <p class="card-text">Unsaved Matches</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-success">{{ selectedAdvert.contactsNotifiedAbout }}</h4>
                <p class="card-text">Contacts Notified</p>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 [ngClass]="getEffectivenessBadgeClass(selectedAdvert.notificationEffectivenessRate)">
                  {{ selectedAdvert.notificationEffectivenessRate | number:'1.1-1' }}%
                </h4>
                <p class="card-text">Effectiveness</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Advert Details -->
        <div class="row mb-4">
          <div class="col-md-6">
            <h6 class="font-weight-bold">Advert Information</h6>
            <table class="table table-sm">
              <tr>
                <td><strong>VRM:</strong></td>
                <td>{{ selectedAdvert.vrm }}</td>
              </tr>
              <tr>
                <td><strong>Added:</strong></td>
                <td>{{ selectedAdvert.added | date:'medium' }}</td>
              </tr>
              <tr>
                <td><strong>Last Updated:</strong></td>
                <td>{{ selectedAdvert.updated | date:'medium' }}</td>
              </tr>
              <tr>
                <td><strong>Advert Status:</strong></td>
                <td>
                  <span class="badge" [ngClass]="getAdvertStatusBadgeClass(selectedAdvert.advertStatus)">
                    {{ selectedAdvert.advertStatus }}
                  </span>
                </td>
              </tr>
              <tr>
                <td><strong>Sold Status:</strong></td>
                <td>
                  <span class="badge" [ngClass]="getSoldStatusBadgeClass(selectedAdvert.soldStatus)">
                    {{ selectedAdvert.soldStatus }}
                  </span>
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <h6 class="font-weight-bold">Notification Performance</h6>
            <table class="table table-sm">
              <tr>
                <td><strong>Effectiveness Rating:</strong></td>
                <td>
                  <span class="badge" [ngClass]="getEffectivenessBadgeClass(selectedAdvert.notificationEffectivenessRate)">
                    {{ selectedAdvert.notificationEffectiveness }}
                  </span>
                </td>
              </tr>
              <tr>
                <td><strong>Notification Rate:</strong></td>
                <td>{{ selectedAdvert.notificationEffectivenessRate | number:'1.1-1' }}%</td>
              </tr>
              <tr>
                <td><strong>Total Matches:</strong></td>
                <td>{{ selectedAdvert.matchedInSavedSearchCount + selectedAdvert.matchedInUnsavedSearchCount }}</td>
              </tr>
              <tr>
                <td><strong>Match Ratio:</strong></td>
                <td>
                  <div class="progress" style="height: 20px;" *ngIf="(selectedAdvert.matchedInSavedSearchCount + selectedAdvert.matchedInUnsavedSearchCount) > 0">
                    <div class="progress-bar bg-primary" role="progressbar"
                         [style.width.%]="(selectedAdvert.matchedInSavedSearchCount / (selectedAdvert.matchedInSavedSearchCount + selectedAdvert.matchedInUnsavedSearchCount)) * 100">
                      Saved: {{ selectedAdvert.matchedInSavedSearchCount }}
                    </div>
                    <div class="progress-bar bg-secondary" role="progressbar"
                         [style.width.%]="(selectedAdvert.matchedInUnsavedSearchCount / (selectedAdvert.matchedInSavedSearchCount + selectedAdvert.matchedInUnsavedSearchCount)) * 100">
                      Unsaved: {{ selectedAdvert.matchedInUnsavedSearchCount }}
                    </div>
                  </div>
                  <span *ngIf="(selectedAdvert.matchedInSavedSearchCount + selectedAdvert.matchedInUnsavedSearchCount) === 0" class="text-muted">
                    No matches found
                  </span>
                </td>
              </tr>
            </table>
          </div>
        </div>

        <!-- Performance Insights -->
        <div class="row">
          <div class="col-12">
            <h6 class="font-weight-bold mb-3">Performance Insights</h6>
            <div class="alert"
                 [class.alert-success]="selectedAdvert.notificationEffectivenessRate >= 70"
                 [class.alert-warning]="selectedAdvert.notificationEffectivenessRate >= 40 && selectedAdvert.notificationEffectivenessRate < 70"
                 [class.alert-danger]="selectedAdvert.notificationEffectivenessRate < 40">
              <div *ngIf="selectedAdvert.notificationEffectivenessRate >= 70">
                <i class="fas fa-check-circle me-2"></i>
                <strong>Excellent Performance:</strong> This advert has a high notification effectiveness rate, indicating good match quality with saved searches.
              </div>
              <div *ngIf="selectedAdvert.notificationEffectivenessRate >= 40 && selectedAdvert.notificationEffectivenessRate < 70">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Moderate Performance:</strong> This advert shows moderate notification effectiveness. Consider reviewing search criteria matching.
              </div>
              <div *ngIf="selectedAdvert.notificationEffectivenessRate < 40">
                <i class="fas fa-times-circle me-2"></i>
                <strong>Poor Performance:</strong> This advert has low notification effectiveness. Review why matches aren't converting to notifications.
              </div>
            </div>

            <!-- Additional Insights based on data -->
            <div class="row mt-3">
              <div class="col-md-6">
                <div class="card border-left-info">
                  <div class="card-body">
                    <h6 class="card-title">
                      <i class="fas fa-info-circle text-info me-2"></i>
                      Match Analysis
                    </h6>
                    <p class="card-text small mb-0">
                      <span *ngIf="selectedAdvert.matchedInSavedSearchCount > selectedAdvert.matchedInUnsavedSearchCount">
                        This advert primarily matches saved searches ({{ selectedAdvert.matchedInSavedSearchCount }} vs {{ selectedAdvert.matchedInUnsavedSearchCount }}),
                        indicating good alignment with customer preferences.
                      </span>
                      <span *ngIf="selectedAdvert.matchedInSavedSearchCount <= selectedAdvert.matchedInUnsavedSearchCount">
                        This advert has more unsaved matches ({{ selectedAdvert.matchedInUnsavedSearchCount }}) than saved searches ({{ selectedAdvert.matchedInSavedSearchCount }}),
                        suggesting potential for new customer interest.
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="card border-left-warning">
                  <div class="card-body">
                    <h6 class="card-title">
                      <i class="fas fa-lightbulb text-warning me-2"></i>
                      Recommendations
                    </h6>
                    <p class="card-text small mb-0">
                      <span *ngIf="selectedAdvert.notificationEffectivenessRate < 30">
                        Consider reviewing why notifications aren't being sent despite matches. Check notification settings and customer preferences.
                      </span>
                      <span *ngIf="selectedAdvert.notificationEffectivenessRate >= 30 && selectedAdvert.notificationEffectivenessRate < 70">
                        Good notification rate but room for improvement. Review search criteria precision and notification timing.
                      </span>
                      <span *ngIf="selectedAdvert.notificationEffectivenessRate >= 70">
                        Excellent performance! This advert is well-matched to customer saved searches and notification preferences.
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="hideModal()">Close</button>
        <button type="button" class="btn btn-primary" onclick="window.print()">
          <i class="fas fa-print me-2"></i>
          Print Details
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Backdrop -->
<div class="modal-backdrop fade" [class.show]="showModal" *ngIf="showModal"></div>

<!-- Filters Card -->
<div class="card shadow mb-4">
  <div class="card-header">
    <h6 class="m-0 font-weight-bold text-primary">
      <i class="fas fa-filter me-2"></i>
      Filters
    </h6>
  </div>
  <div class="card-body">
    <div class="row">
      <!-- Date Range -->
      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">From Date</label>
        <input
          type="date"
          class="form-control form-control-sm"
          [(ngModel)]="fromDate"
          (ngModelChange)="onFilterChanged()">
      </div>
      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">To Date</label>
        <input
          type="date"
          class="form-control form-control-sm"
          [(ngModel)]="toDate"
          (ngModelChange)="onFilterChanged()">
      </div>

      <!-- VRM Search -->
      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">VRM</label>
        <input
          type="text"
          class="form-control form-control-sm"
          placeholder="Search by VRM..."
          [(ngModel)]="vrmFilter"
          (ngModelChange)="onFilterChanged()">
      </div>

      <!-- Status Filters -->
      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">Advert Status</label>
        <select
          class="form-control form-control-sm"
          [(ngModel)]="advertStatusFilter"
          (ngModelChange)="onFilterChanged()">
          <option value="">All Statuses</option>
          <option *ngFor="let status of advertStatusOptions" [value]="status.value">
            {{ status.text }}
          </option>
        </select>
      </div>

      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">Sold Status</label>
        <select
          class="form-control form-control-sm"
          [(ngModel)]="soldStatusFilter"
          (ngModelChange)="onFilterChanged()">
          <option value="">All</option>
          <option *ngFor="let status of soldStatusOptions" [value]="status.value">
            {{ status.text }}
          </option>
        </select>
      </div>

      <!-- Notification Rate Range -->
<!--      <div class="col-lg-3 col-md-6 mb-3">-->
<!--        <label class="form-label">Min Notification Rate %</label>-->
<!--        <input-->
<!--          type="number"-->
<!--          class="form-control form-control-sm"-->
<!--          placeholder="0"-->
<!--          min="0"-->
<!--          max="100"-->
<!--          [(ngModel)]="minNotificationRate"-->
<!--          (ngModelChange)="onFilterChanged()">-->
<!--      </div>-->
<!--      <div class="col-lg-3 col-md-6 mb-3">-->
<!--        <label class="form-label">Max Notification Rate %</label>-->
<!--        <input-->
<!--          type="number"-->
<!--          class="form-control form-control-sm"-->
<!--          placeholder="100"-->
<!--          min="0"-->
<!--          max="100"-->
<!--          [(ngModel)]="maxNotificationRate"-->
<!--          (ngModelChange)="onFilterChanged()">-->
<!--      </div>-->

      <div class="col-lg-3 col-md-6 mb-3">
        <label class="form-label">Vendor</label>
        <select
          class="form-control form-control-sm"
          [(ngModel)]="vendorFilter"
          (ngModelChange)="onFilterChanged()">
          <option value="">All</option>
          <option *ngFor="let vendor of vendors" [value]="vendor.id">
            {{ vendor.vendorName }}
          </option>
        </select>
      </div>



      <!-- Clear Filters Button -->
      <div class="col-lg-3 col-md-6 mb-3 d-flex align-items-end">
        <button
          class="btn btn-outline-secondary btn-sm w-100"
          (click)="clearFilters()">
          <i class="fas fa-times me-2"></i>
          Clear Filters
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Error State -->
<div *ngIf="error" class="alert alert-danger" role="alert">
  <i class="fas fa-exclamation-triangle me-2"></i>
  {{ error }}
</div>

<!-- Data Table -->
<div class="card shadow">
  <div class="card-header">
    <div class="d-flex justify-content-between align-items-center">
      <h6 class="m-0 font-weight-bold text-primary">
        Advert Analytics
        <span *ngIf="totalCount > 0" class="badge badge-secondary ms-2">{{ totalCount }}</span>
      </h6>
      <div *ngIf="loading" class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
  </div>

  <div class="card-body p-0">
    <div class="table-responsive">
      <table class="table table-striped table-hover mb-0">
        <thead class="thead-light">
        <tr>
          <th scope="col" class="cursor-pointer" (click)="onSort('vrm')">
            VRM
            <i class="ms-1" [ngClass]="getSortIcon('vrm')"></i>
          </th>
          <th scope="col" class="cursor-pointer" (click)="onSort('customerName')">
            Vendor
            <i class="ms-1" [ngClass]="getSortIcon('customerName')"></i>
          </th>
          <th scope="col" class="cursor-pointer" (click)="onSort('advertStatus')">
            Advert Status
            <i class="ms-1" [ngClass]="getSortIcon('advertStatus')"></i>
          </th>
          <th scope="col" class="cursor-pointer" (click)="onSort('soldStatus')">
            Sold Status
            <i class="ms-1" [ngClass]="getSortIcon('soldStatus')"></i>
          </th>
          <th scope="col" class="cursor-pointer" (click)="onSort('added')">
            Added
            <i class="ms-1" [ngClass]="getSortIcon('added')"></i>
          </th>
          <th>
            Search Matches
          </th>
          <th>
            Contacts Notified
          </th>
          <th>
            Effectiveness
          </th>
          <th scope="col">Actions</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let advert of adverts | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage, totalItems: totalCount }">
          <td>
            <span class="font-weight-bold">{{ advert.vrm }}</span>
            <div>
              <small>{{ advert.description }}</small>
            </div>
            <div class="d-flex flex-row" style="justify-content: space-between;">
              <div class="btn btn-primary btn-xs">
                <a [routerLink]="['/admin/manage-negotiations', advert.advertId]" target="_blank">
                  Brokerage
                </a>
              </div>
              <div class="btn btn-secondary btn-xs">
                <a [routerLink]="['/main/listing', advert.advertId]" target="_blank">
                  View Listing
                </a>
              </div>
            </div>
          </td>
          <td>
            <a [routerLink]="['/admin/customer', advert.customerId]" target="_blank">{{ advert.customerName }}</a>
          </td>
          <td>
                <span class="badge" [ngClass]="getAdvertStatusBadgeClass(advert.advertStatus)">
                  {{ getAdvertStatusText(advert.advertStatus) }}
                </span>
          </td>
          <td>
                <span class="badge" [ngClass]="getSoldStatusBadgeClass(advert.soldStatus)">
                  {{ getSoldStatusText(advert.soldStatus) }}
                </span>
          </td>
          <td>
            <small>{{ advert.added | date:'short' }}</small>
          </td>
          <td>
            <div class="d-flex flex-column">
              <span class="badge badge-primary mb-1">{{ advert.matchedInSavedSearchCount }} Saved</span>
              <span class="badge badge-secondary">{{ advert.matchedInUnsavedSearchCount }} Unsaved</span>
            </div>
          </td>
          <td>
            <span class="badge badge-success">{{ advert.contactsNotifiedAbout }}</span>
          </td>
          <td>
            <div class="d-flex flex-column">
                  <span
                    class="badge mb-1"
                    [ngClass]="getEffectivenessBadgeClass(advert.notificationEffectivenessRate)">
                    {{ advert.notificationEffectivenessRate | number:'1.1-1' }}%
                  </span>
              <small class="text-muted">{{ advert.notificationEffectiveness }}</small>
            </div>
          </td>
          <td>
            <button class="btn btn-outline-primary btn-sm" (click)="showAdvertDetails(advert)">
              Details
            </button>
          </td>
        </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && adverts.length === 0" class="text-center py-5">
      <i class="fas fa-car fa-3x text-muted mb-3"></i>
      <h5 class="text-muted">No adverts found</h5>
      <p class="text-muted">Try adjusting your filters or search criteria.</p>
    </div>
  </div>

  <!-- Pagination -->
  <div class="card-footer" *ngIf="totalCount > 0">
    <div class="d-flex justify-content-between align-items-center">
      <div class="text-muted">
        Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to
        {{ Math.min(currentPage * itemsPerPage, totalCount) }} of
        {{ totalCount }} adverts
      </div>
      <div>
        Items per Page:
        <select (change)="handlePageSizeChange($event)">
          <option *ngFor="let size of pageSizes" [ngValue]="size">
            {{ size }}
          </option>
        </select>
      </div>
      <pagination-controls
        id="paginator"
        (pageChange)="onPageChanged($event)"
        [directionLinks]="true"
        previousLabel="Previous"
        nextLabel="Next">
      </pagination-controls>
    </div>
  </div>


<!--  <div class="alert alert-info mb-3">-->
<!--    <h6>Manual Pagination Test:</h6>-->
<!--    <div class="btn-group" role="group">-->
<!--      <button-->
<!--        *ngFor="let pageNum of [1,2,3,4,5]"-->
<!--        class="btn btn-sm"-->
<!--        [class.btn-primary]="pageNum === currentPage"-->
<!--        [class.btn-outline-primary]="pageNum !== currentPage"-->
<!--        (click)="manualPageChange(pageNum)">-->
<!--        {{ pageNum }}-->
<!--      </button>-->
<!--    </div>-->
<!--    <p class="mt-2 mb-0">-->
<!--      <small>If this works but pagination-controls doesn't, there's an issue with the component configuration.</small>-->
<!--    </p>-->
<!--  </div>-->
