<div class="container-fluid">
  <!-- Navigation Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header border-0">
          <div class="d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0 text-gray-800">
              <i class="fas fa-chart-line me-2"></i>
              Search Analytics Dashboard
            </h1>
            <div class="text-muted">
              <i class="fas fa-clock me-1"></i>
              <!-- Last updated: {{ new Date() | date:'short' }} -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Tab Navigation -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-body p-0">
          <nav class="nav nav-tabs nav-fill" id="nav-tab" role="tablist">
            <a
              class="nav-item nav-link cursor-pointer"
              [class.active]="isActiveTab('dashboard')"
              (click)="setActiveTab('dashboard')"
              role="tab">
              <i class="fas fa-tachometer-alt me-2"></i>
              Dashboard Overview
            </a>
            <a
              class="nav-item nav-link cursor-pointer"
              [class.active]="isActiveTab('customers')"
              (click)="setActiveTab('customers')"
              role="tab">
              <i class="fas fa-users me-2"></i>
              Customer Analytics
            </a>
            <a
              class="nav-item nav-link cursor-pointer"
              [class.active]="isActiveTab('adverts')"
              (click)="setActiveTab('adverts')"
              role="tab">
              <i class="fas fa-car me-2"></i>
              Advert Analytics
            </a>
            <a
              class="nav-item nav-link cursor-pointer"
              [class.active]="isActiveTab('search-analytics')"
              (click)="setActiveTab('search-analytics')"
              role="tab">
              <i class="fas fa-envelope-open-text me-2"></i>
              Email Contact Issues
            </a>
          </nav>
        </div>
      </div>
    </div>
  </div>

  <!-- Tab Content -->
  <div class="tab-content">
    <!-- Dashboard Tab -->
    <div class="tab-pane fade" [class.show]="isActiveTab('dashboard')" [class.active]="isActiveTab('dashboard')">
      <app-search-stats-dashboard *ngIf="isActiveTab('dashboard')"></app-search-stats-dashboard>
    </div>

    <!-- Advert Analytics Tab -->
    <div class="tab-pane fade" [class.show]="isActiveTab('adverts')" [class.active]="isActiveTab('adverts')">
      <app-advert-analytics *ngIf="isActiveTab('adverts')"></app-advert-analytics>
    </div>

    <!-- Customer Analytics Tab -->
    <div class="tab-pane fade" [class.show]="isActiveTab('customers')" [class.active]="isActiveTab('customers')">
      <app-customer-analytics *ngIf="isActiveTab('customers')"></app-customer-analytics>
    </div>

    <!-- Email Contact Issues Tab -->
    <div class="tab-pane fade" [class.show]="isActiveTab('search-analytics')" [class.active]="isActiveTab('search-analytics')">
      <app-email-contact-issues *ngIf="isActiveTab('search-analytics')"></app-email-contact-issues>
    </div>

  </div>
</div>
