import { Component, OnInit } from '@angular/core';
import { EmailContactIssueDTO, ContactIssueSearchDTO } from '../../../global/interfaces';
import { AdminSearchStatsService } from '../services';

@Component({
  selector: 'app-email-contact-issues',
  templateUrl: './email-contact-issues.component.html',
  styleUrls: ['./email-contact-issues.component.scss']
})
export class EmailContactIssuesComponent implements OnInit {
  contactIssues: EmailContactIssueDTO[] = [];
  totalItems = 0;
  currentPage = 1;
  itemsPerPage = 25;
  loading = false;
  exporting = false;
  error: string | null = null;

  // Pagination options
  pageSizes = [10, 25, 50, 100];

  // Filter properties
  issueTypeFilter = '';
  fromDate: string;
  toDate: string;

  // Sorting
  sortColumn = 'issueDate';
  sortDescending = true;

  // Available issue types for filter dropdown
  issueTypes = [
    { value: '', label: 'All Issue Types' },
    { value: 'unsubscribed', label: 'Unsubscribed' },
    { value: 'blocked', label: 'Blocked' },
    { value: 'hardBounce', label: 'Hard Bounce' },
    { value: 'spam', label: 'Spam' }
  ];

  constructor(private searchAnalyticsService: AdminSearchStatsService) {
    // Set default date range to last 1 year
    const today = new Date();
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(today.getFullYear() - 1);

    this.toDate = this.formatDateForInput(today);
    this.fromDate = this.formatDateForInput(oneYearAgo);
  }

  async ngOnInit() {
    await this.loadContactIssues();
  }

  async loadContactIssues() {
    this.loading = true;
    this.error = null;

    try {
      const searchDTO = this.buildSearchDTO();
      const result = await this.searchAnalyticsService.getContactIssues(searchDTO);

      this.contactIssues = result.results || [];
      this.totalItems = result.totalItems || 0;
    } catch (error) {
      this.error = 'Failed to load contact issues';
      console.error('Contact issues load error:', error);
    } finally {
      this.loading = false;
    }
  }

  async onPageChanged(page: number) {
    this.currentPage = page;
    await this.loadContactIssues();
  }

  async onFilterChanged() {
    this.currentPage = 1; // Reset to first page when filters change
    await this.loadContactIssues();
  }

  async onSort(column: string) {
    if (this.sortColumn === column) {
      this.sortDescending = !this.sortDescending;
    } else {
      this.sortColumn = column;
      this.sortDescending = true;
    }

    this.currentPage = 1;
    await this.loadContactIssues();
  }

  async exportToCSV() {
    this.exporting = true;

    try {
      // Note: You may need to implement this method in the service
      // await this.searchAnalyticsService.exportContactIssues(this.buildSearchDTO());
      console.log('Export functionality not yet implemented');
    } catch (error) {
      console.error('Export error:', error);
    } finally {
      this.exporting = false;
    }
  }

  clearFilters() {
    this.issueTypeFilter = '';
    const today = new Date();
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(today.getFullYear() - 1);

    this.toDate = this.formatDateForInput(today);
    this.fromDate = this.formatDateForInput(oneYearAgo);
    this.onFilterChanged();
  }

  getIssueTypeBadgeClass(issueType: string): string {
    switch (issueType.toLowerCase()) {
      case 'unsubscribed': return 'badge-warning';
      case 'blocked': return 'badge-danger';
      case 'hardbounce': return 'badge-danger';
      case 'spam': return 'badge-danger';
      default: return 'badge-secondary';
    }
  }

  getSortIcon(column: string): string {
    if (this.sortColumn !== column) {
      return 'fas fa-sort text-muted';
    }
    return this.sortDescending ? 'fas fa-sort-down text-primary' : 'fas fa-sort-up text-primary';
  }

  handlePageSizeChange(event: any) {
    this.itemsPerPage = event.target.value;
    this.currentPage = 1;
    this.loadContactIssues();
  }

  private formatDateForInput(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  private parseInputDate(dateString: string): Date {
    return new Date(dateString + 'T00:00:00');
  }

  private buildSearchDTO(): ContactIssueSearchDTO {
    const offset = (this.currentPage - 1) * this.itemsPerPage;

    return {
      offset,
      limit: this.itemsPerPage,
      filters: {
        startDate: this.parseInputDate(this.fromDate),
        endDate: this.parseInputDate(this.toDate),
        issueType: this.issueTypeFilter || undefined
      },
      order: [{ column: this.sortColumn, descending: this.sortDescending }]
    };
  }
}
