<form [formGroup]="filterForm" (submit)="submitSearch()">

  <div class="mt-1"
       style="display: grid; grid-gap: 10px; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));">

    <div class="md-form input-sm mb-0">
      <input
        mdbInput
        type="text"
        name="filter-search"
        class="form-control float-label mb-0" formControlName="phrase" id="filter-search">
      <label for="filter-search">Search</label>
    </div>

    <div class="select select-sm">
      <mdb-select-2 [outline]="true"
                    formControlName="statusId"
                    class="white-background"
                    placeholder="Select Status">
        <mdb-select-option *ngFor="let option of statusOptions" [value]="option.value">{{ option.label }}
        </mdb-select-option>
      </mdb-select-2>
    </div>


    <div class="select select-sm">
      <mdb-select-2 [outline]="true"
                    formControlName="customerType"
                    class="white-background"
                    placeholder="Customer Type">
        <mdb-select-option *ngFor="let option of customerTypeOptions" [value]="option.value">{{ option.label }}
        </mdb-select-option>
      </mdb-select-2>
    </div>
    <div class="select select-sm">
      <mdb-select-2 [outline]="true"
                    class="white-background"
                    formControlName="whoswhoStatus"
                    placeholder="Call Status">
        <mdb-select-option *ngFor="let option of whoswhoStatusOptions" [value]="option.value">{{ option.label }}
        </mdb-select-option>
      </mdb-select-2>
    </div>
    <div class="select select-sm">
      <mdb-select-2 [outline]="true"
                    formControlName="assignedTo"
                    class="white-background"
                    placeholder="Assigned To">
        <mdb-select-option *ngFor="let option of assignedOptions"
                           [value]="option.value" class="adminstatus-{{ option.statusId }}">{{ option.label }}
        </mdb-select-option>
      </mdb-select-2>
    </div>
    <div style="text-align: center;">

      <div class="d-flex grid-gap-10" style="padding-top: 5px;">

        <div>
          <ng-toggle
            [width]="95"
            [color]="{ checked: 'var(--successColour)', unchecked: 'var(--errorColour)' }"
            [fontColor]="{ checked: '#fff', unchecked: '#fff' }"
            [labels]="{ checked: 'Buyers', unchecked: 'No Buyers' }"
            formControlName="isBuyer"></ng-toggle>
        </div>
        <div>
          <ng-toggle
            [width]="105"
            [color]="{ checked: 'var(--successColour)', unchecked: 'var(--errorColour)' }"
            [fontColor]="{ checked: '#fff', unchecked: '#fff' }"
            [labels]="{ checked: 'Vendors', unchecked: 'No Vendors' }"
            formControlName="isSeller"></ng-toggle>
        </div>
      </div>

    </div>
    <div style="position: relative; min-height: 40px;">
      <input type="reset" class="btn btn-sm" value="Reset" (click)="clearQuery()">
      <input type="submit" class="btn btn-tertiary btn-sm" value="Search">
    </div>

    <div style="position: relative; min-height: 40px;">
      <button [disabled]="downloadingCSV" class="btn btn-sm" (click)="downloadCSV()">
        <i class="fa fa-download"></i> CSV
      </button>
    </div>
  </div>
</form>

<div *ngIf="isLoadingPending; else idPendingApproval" class="text-center p-2"><i class="fa fa-spin fa-spinner"></i>
  Checking for pending IDs
</div>

<div *ngIf="isLoadingBidLimited; else bidLimitReached" class="text-center p-2"><i class="fa fa-spin fa-spinner"></i>
  Checking for bid limited customers
</div>

<app-admin-unverified-contacts [showDeleted]="false"></app-admin-unverified-contacts>

<div *ngIf="isLoading; else mainData">
  <div class="pt-4 pb-4 text-center">
    <i class="fa fa-2x fa-spin fa-spinner"></i>
    <h1 class="mt-2">Searching</h1>
  </div>
</div>

<ng-template #mainData>

  <div *ngIf="aggregateData.length === 0" class="text-center px-2 py-4">
    <h1>No results found</h1>
  </div>

  <div class="widget" style="overflow-x: auto" *ngIf="aggregateData.length > 0">

    <table class="table table-striped table-hover table-compressed table-narrow whoswho-table"
           style="width: 100%;">
      <thead>
      <tr>
        <th class="text-center"><i class="fa fa-comment"></i></th>
        <th class="text-center"><i class="fa fa-phone-alt"></i></th>
        <th>Customer</th>
        <th>Contact</th>
        <th class="ball-header">
          <span class="ball-label" title="Docs Pending">D</span>
          <span class="ball-label" title="Logged in Recently">L</span>
          <span class="ball-label" title="Saved Search">S</span>
          <span class="ball-label" title="Post Advert">A</span>
          <span class="ball-label" title="WWW correct">W</span>
          <span class="ball-label" title="Payment Method Set">P</span>
        </th>
        <th class="number-cell">Logins</th>
        <th class="number-cell">Failures</th>
        <th class="number-cell">Ad Views</th>
        <th class="number-cell">Purchases</th>
        <th class="number-cell">Sales</th>
        <th class="number-cell">Searches</th>
        <th class="number-cell">Bids</th>
        <th>Status</th>
        <th>Assigned</th>
        <th>Added/Updated</th>
      </tr>
      </thead>
      <tbody>
      <tr
        *ngFor="let data of sorted(aggregateData) | paginate: { itemsPerPage: pageSize, currentPage: page, totalItems: count }"
        (click)="onRowClick(data)"
        [class.seller-border]="data.customer.isSeller">

        <td class="align-middle text-right" style="min-width: 40px; width: 40px;">
          <i *ngIf="data.customer.adminTasks?.length == 0" (click)="showNotes($event, data.customer)"
             class="note-icon far fa-comment-alt mt-1"></i>
          <i *ngIf="data.customer.adminTasks?.length > 0" (click)="showNotes($event, data.customer)"
             class="note-icon fa fa-exclamation-triangle text-danger"></i>
        </td>

        <td class="valign-middle">
          <span class="call-ball"
                [title]="whosWhoStatusName[data.customer?.customerInternalInfo?.whosWhoStatus ?? 0].name ?? ''"
                [class]="'call-ball-' + [data.customer?.customerInternalInfo?.whosWhoStatus ?? 0]">
            {{ whosWhoStatusTitle[data.customer?.customerInternalInfo?.whosWhoStatus ?? 0].name }}
          </span>
        </td>
        <td>
          <div class="table-line-1">{{ data.customer.customerName }}</div>
          <div class="table-line-2"
               style="overflow-y: hidden; max-width: 150px; overflow-x: hidden; text-overflow: ellipsis">{{ data.customer.email }}
          </div>
          <div class="table-line-2">{{ data.customer.phone1 }}</div>
        </td>
        <td>
          <div class="table-line-1">{{ data.customer.primaryContact.contactName }}</div>
          <div class="table-line-2"
               style="overflow-y: hidden; max-width: 150px; overflow-x: hidden; text-overflow: ellipsis">{{ data.customer.primaryContact.email }}
          </div>
          <div class="table-line-2">{{ data.customer.primaryContact.phone1 }}</div>
        </td>
        <td>
          <div class="balls">
            <div class="ball" [class]="idPendingBall(data.customer.customerInternalInfo)"
                 title="Documents Uploaded"></div>
            <div class="ball" [class]="loggedInBall(data.customer.lastLogin)" title="Recent Login"></div>
            <div class="ball" [class]="savedSearchBall(data.customer.customerInternalInfo?.alertConfigured)"
                 title="Saved Search"></div>
            <div class="ball" [class]="advertsPlacedBall(data.customer.totalAdverts)" title="Advert Placed"></div>
            <div class="ball" [class]="wwwBall(data.customer.websiteUrl)" title="WWW Configured"></div>
            <div class="ball" [class]="paymentMethodSet(data.customer.customerInternalInfo?.hasPaymentMethod)"
                 title="Payment Method Set"></div>
          </div>

          <div class="customer-types">
            <span class="isBuyer mr-1 btn btn-xxs" *ngIf="data.customer.isBuyer">Buyer</span>
            <span class="isSeller btn btn-xxs" *ngIf="data.customer.isSeller">Vendor</span>
          </div>
        </td>
        <td class="number-cell">{{ data.loginCount }}</td>
        <td class="number-cell">{{ data.failedLoginCount }}</td>
        <td class="number-cell">{{ data.adviewsCount }}</td>
        <td class="number-cell">{{ data.buyerDealsCount }}</td>
        <td class="number-cell">{{ data.sellerDealsCount }}</td>
        <td class="number-cell">{{ data.searchCount }}</td>
        <td class="number-cell">{{ data.bidCount }}</td>
        <td>
          <div>
            <span class="btn btn-xs status status-{{ data.customer.statusId }}">{{ statusName[data.customer.statusId] }}</span>
          </div>

        </td>
        <td
          (click)="assignCustomer($event, data.customerId, data.customer?.customerInternalInfo?.assignedTo)">
              <span class="btn btn-xs assigned-pill isAssigned-{{ data.customer?.customerInternalInfo?.assignedTo != null }}">
                {{ adminLookup[data?.customer?.customerInternalInfo?.assignedTo] || "Unassigned" }}
              </span>
        </td>
        <td class="shrink-cell">
          <div class="table-line-1">{{ data.customer.added | date: 'dd MMM YYYY HH:mm' }}</div>
          <div class="table-line-2">{{ data.customer.updated | date: 'dd MMM YYYY HH:mm' }}</div>
        </td>
      </tr>
      </tbody>
    </table>

  </div>
  <div class="pagination-divider mb-3">
    <div class="row">
      <div class="col-6 pagination-page-size">
        Items per Page:
        <select (change)="handlePageSizeChange($event)">
          <option *ngFor="let size of pageSizes" [ngValue]="size">
            {{ size }}
          </option>
        </select>
      </div>

      <div class="col-6 text-right">
        <pagination-controls
          responsive="true"
          class="paginator"
          previousLabel="Prev"
          nextLabel="Next"
          (pageChange)="onTableDataChange($event)">
        </pagination-controls>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #idPendingApproval>

  <div *ngIf="pendingCustomers.length > 0" class="pt-3 pb-3">
    <h2 style="font-size: 1.1rem; letter-spacing: -0.25px;"><i class="fa fa-exclamation-triangle text-danger"></i>
      Pending Approval <span class="pending-badge">{{ pendingCustomers.length }}</span></h2>
    <table class="table table-striped whoswho-table" style="background-color: #fff">
      <thead>
      <tr>
        <th style="text-align: center;"><i class="fa fa-comment"></i></th>
        <th>Customer</th>
        <th>Contact</th>
        <th>Documents</th>
        <th>Assigned</th>
        <th>Added/Updated</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let internalInfo of pendingCustomers" (click)="onRowClick(internalInfo)">
        <td class="align-middle text-center" style="min-width: 40px; width: 40px;">
          <i (click)="showNotes($event, internalInfo.customer)" class="note-icon far fa-comment-alt mt-1"></i>
        </td>
        <td>
          <div class="table-line-1">{{ internalInfo.customer.customerName }}</div>
          <div class="table-line-2">{{ internalInfo.customer.email }}</div>
        </td>
        <td>
          <div class="table-line-1">{{ internalInfo.customer.primaryContact?.contactName }}</div>
          <div class="table-line-2">{{ internalInfo.customer.primaryContact?.email }}</div>
        </td>
        <td>
          <span *ngFor="let x of filteredMedia(internalInfo.customer?.customerMedias)">
            <span class="btn btn-xs btn-primary document-container"
                  (click)="x.mediaType == mediaTypeEnum.Image ? showImage($event, x) : showDocument($event, x)">
              <span style="font-size: 1rem">
              <i class="far fa-image" *ngIf="x.mediaType == mediaTypeEnum.Image"></i>
              <i class="far fa-file" *ngIf="x.mediaType == mediaTypeEnum.Document"></i>
              </span>
              {{ subCategoryName(x.mediaSubCategory) }}
            </span>
          </span>
        </td>
        <td>
          <div class="table-line-1">
            <span class="assigned-pill isAssigned-{{ internalInfo.assignedTo != null }}"
                  (click)="assignCustomer($event, internalInfo.customer.id, internalInfo.assignedTo)">
            {{ adminLookup[internalInfo.assignedTo] || 'Unassigned' }}
            </span>
          </div>
        </td>
        <td class="shrink-cell">
          <div class="table-line-1">{{ internalInfo.customer.added | date: "dd MMM yyyy HH:mm" }}</div>
          <div class="table-line-2">{{ internalInfo.customer.updated | date: "dd MMM yyyy HH:mm" }}</div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>

</ng-template>

<app-admin-assign-customer-dialog (customerAssignedEvent)="assignCustomerClosed($event)"
                                  [customerInternalInfo]="customerInternalInfo"
                                  [showAssignDialog]="showAssignDialog"></app-admin-assign-customer-dialog>

<!--
<div mdbModal #assignCustomerModal="mdbModal" class="modal fade"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{backdrop: 'static'}">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">

        <button type="button" class="close pull-right" aria-label="Close" (click)="assignCustomerModal.hide()"><span
          aria-hidden="true">×</span></button>

        <div>Assign Customer</div>

      </div>
      <div class="modal-body assign">

        <form [formGroup]="assignForm">

          <div class="narrow-select">
            <mdb-select-2
              [outline]="true"
              [label]="'Assign To'"
              style="width: 100%;"
              formControlName="assignedTo"
              placeholder="Select an assignee">
              <mdb-select-option [value]="null">Select an assignee</mdb-select-option>
              <mdb-select-option *ngFor="let user of realAssignees(assignedOptions)"
                                 [disabled]="user.disabled"
                                 class="adminstatus-{{ user.statusId }}"
                                 [value]="user.value">{{ user.label }}
              </mdb-select-option>
            </mdb-select-2>
          </div>

          <div class="mt-3">
            <div class="md-form">
              <textarea mdbInput class="form-control float-label" id="comment"
                        formControlName="assignComment"></textarea>
              <label for="comment">Comment</label>
            </div>

          </div>

          <div class="text-right">
            <div class="btn btn-primary-outline mr-2" (click)="assignCustomerModal.hide()">Cancel</div>
            <div class="btn btn-primary" (click)="submitAssignForm()">Submit</div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
-->

<div mdbModal #notesModal="mdbModal" class="modal fade" tabindex="-1"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{backdrop: 'static'}">

  <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header narrow">
        <button type="button" class="close pull-right" aria-label="Close" (click)="notesModal.hide()"><span
          aria-hidden="true">×</span></button>
        <div class="modal-title" id="offerModalLabel">
          Notes
        </div>
      </div>
      <div class="modal-body" style="position: relative; padding: 7px;">
        <app-customer-notes2
          [getNotes]="true"
          [notesCustomer]="notesCustomer"
          (statusChanged)="noteSaved()"></app-customer-notes2>
      </div>
    </div>
  </div>
</div>

<div mdbModal #imageModal="mdbModal" class="modal fade" tabindex="-1"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{backdrop: 'static'}">

  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header narrow">
        <button type="button" class="close pull-right" aria-label="Close" (click)="imageModal.hide()"><span
          aria-hidden="true">×</span></button>
        <h3 class="modal-title"><strong>Image</strong></h3>
      </div>
      <div class="modal-body" style="position: relative; height: 90%; text-align: center;">

        <img [src]="imageSrc" style="max-width: 100%; max-height: 100%;" alt="Image"/>

      </div>
    </div>
  </div>
</div>


<ng-template #bidLimitReached>

  <div *ngIf="bidLimitCustomers.length > 0" class="pt-3 pb-3">
    <h2 style="font-size: 1.1rem; letter-spacing: -0.25px;"><i class="fa fa-exclamation-triangle text-danger"></i>
      Bid Limit Reached <span class="pending-badge">{{ bidLimitCustomers.length }}</span></h2>
    <table class="table table-striped whoswho-table" style="background-color: #fff">
      <thead>
      <tr>
        <th style="text-align: center;"><i class="fa fa-comment"></i></th>
        <th>Customer</th>
        <th>Contact</th>
        <th>Deal Limit</th>
        <th>Assigned</th>
        <th>Added/Updated</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let internalInfo of bidLimitCustomers" (click)="onRowClick(internalInfo)">
        <td class="align-middle text-center" style="min-width: 40px; width: 40px;">
          <i (click)="showNotes($event, internalInfo.customer)" class="note-icon far fa-comment-alt mt-1"></i>
        </td>
        <td>
          <div class="table-line-1">{{ internalInfo.customer.customerName }}</div>
          <div class="table-line-2">{{ internalInfo.customer.email }}</div>
        </td>
        <td>
          <div class="table-line-1">{{ internalInfo.customer.primaryContact?.contactName }}</div>
          <div class="table-line-2">{{ internalInfo.customer.primaryContact?.email }}</div>
        </td>
        <td>
          {{ internalInfo.incompleteDealCount }}
        </td>
        <td>
          <div class="table-line-1">
            <span class="assigned-pill isAssigned-{{ internalInfo.assignedTo != null }}"
                  (click)="assignCustomer($event, internalInfo.customer.id, internalInfo.assignedTo)">
            {{ adminLookup[internalInfo.assignedTo] || 'Unassigned' }}
            </span>
          </div>
        </td>
        <td class="shrink-cell">
          <div class="table-line-1">{{ internalInfo.customer.added | date: "dd MMM yyyy HH:mm" }}</div>
          <div class="table-line-2">{{ internalInfo.customer.updated | date: "dd MMM yyyy HH:mm" }}</div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>

</ng-template>
