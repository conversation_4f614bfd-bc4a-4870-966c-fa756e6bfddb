import { Injectable } from '@angular/core';
import { ApiService, HandlerService } from "../../../global/services";
import { DataService } from "../../../services";
import {
  AdminCustomerSearchAnalyticsDTO,
  AdvertNotificationStatsDTO,
  AdminSearchSystemOverviewDTO,
  CustomerAnalyticsSearchDTO,
  AdvertAnalyticsSearchDTO,
  TypedSearchResultDTO,
  AdminCustomerSearchFilters,
  AdminAdvertSearchFilters,
  AdminStatVendorDTO,
  AdminStatBasicCustomerDTO,
  EmailContactIssueDTO,
  ContactIssueSearchDTO,
} from '../../../global/interfaces';
import {AdvertStatusEnum, SoldStatusEnum} from '../../../global/enums';

@Injectable()
export class AdminSearchStatsService {
  private serviceUrl = '/api/search-stats';
  private emailServiceUrl = '/api/email-analytics';

  constructor(
    private apiClient: ApiService,
    private data: DataService,
    private handler: HandlerService
  ) {}

  /**
   * Get system overview dashboard metrics
   */
  async getSystemOverview(): Promise<AdminSearchSystemOverviewDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/system-overview`;
    try {
      return await this.apiClient.get({ url }) as AdminSearchSystemOverviewDTO;
    } catch (error) {
      this.handler.handleError(error);
      throw error;
    }
  }

  async getVendors(): Promise<AdminStatVendorDTO[]> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/vendors`;
    try {
      return await this.apiClient.get({ url }) as AdminStatVendorDTO[];
    } catch (error) {
      this.handler.handleError(error);
      throw error;
    }
  }

  async getCustomersWithoutAlerts(): Promise<AdminStatBasicCustomerDTO[]> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/customers-without-alerts`;
    try {
      return await this.apiClient.get({ url }) as AdminStatBasicCustomerDTO[];
    } catch (error) {
      this.handler.handleError(error);
      throw error;
    }
  }

  /**
   * Get customer analytics with pagination and filtering
   */
  async getCustomerAnalytics(searchDTO: CustomerAnalyticsSearchDTO): Promise<TypedSearchResultDTO<AdminCustomerSearchAnalyticsDTO>> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/customer-analytics`;
    try {
      return await this.apiClient.post({
        url,
        data: searchDTO,
        headers: { 'Content-Type': 'application/json' }
      }) as TypedSearchResultDTO<AdminCustomerSearchAnalyticsDTO>;
    } catch (error) {
      this.handler.handleError(error);
      throw error;
    }
  }

  async getContactIssues(searchDTO: ContactIssueSearchDTO): Promise<TypedSearchResultDTO<EmailContactIssueDTO>> {
    const url = `${this.data.apiUrl}${this.emailServiceUrl}/contact-issues`;
    try {
      return await this.apiClient.post({
        url,
        data: searchDTO,
        headers: { 'Content-Type': 'application/json' }
      }) as TypedSearchResultDTO<EmailContactIssueDTO>;
    } catch (error) {
      this.handler.handleError(error);
      throw error;
    }
  }

  /**
   * Get advert analytics with pagination and filtering
   */
  async getAdvertAnalytics(searchDTO: AdvertAnalyticsSearchDTO): Promise<TypedSearchResultDTO<AdvertNotificationStatsDTO>> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/advert-analytics`;
    try {
      return await this.apiClient.post({
        url,
        data: searchDTO,
        headers: { 'Content-Type': 'application/json' }
      }) as TypedSearchResultDTO<AdvertNotificationStatsDTO>;
    } catch (error) {
      this.handler.handleError(error);
      throw error;
    }
  }

  /**
   * Export customer analytics to CSV
   */
  async exportCustomerAnalytics(searchDTO: CustomerAnalyticsSearchDTO): Promise<void> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/customer-analytics`;
    try {
      // Remove pagination for export
      const exportDTO = { ...searchDTO, offset: undefined, limit: undefined };
      const response = await this.apiClient.post({
        url,
        data: exportDTO,
        headers: { 'Content-Type': 'application/json' },
        responseType: 'blob'
      }) as Blob;

      this.downloadFile(response, `customer-analytics-${this.getDateString()}.csv`);
    } catch (error) {
      this.handler.handleError(error);
      throw error;
    }
  }

  /**
   * Export advert analytics to CSV
   */
  async exportAdvertAnalytics(searchDTO: AdvertAnalyticsSearchDTO): Promise<void> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/advert-analytics`;
    try {
      // Remove pagination for export
      const exportDTO = { ...searchDTO, offset: undefined, limit: undefined };
      const response = await this.apiClient.post({
        url,
        data: exportDTO,
        headers: { 'Content-Type': 'application/json' },
        responseType: 'blob'
      }) as Blob;

      this.downloadFile(response, `advert-analytics-${this.getDateString()}.csv`);
    } catch (error) {
      this.handler.handleError(error);
      throw error;
    }
  }

  /**
   * Build default customer search DTO
   */
  buildDefaultCustomerSearchDTO(overrides?: Partial<CustomerAnalyticsSearchDTO>): CustomerAnalyticsSearchDTO {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    return {
      offset: 0,
      limit: 25,
      filters: {
        fromDate: thirtyDaysAgo,
        toDate: new Date()
      },
      order: [{ column: 'notificationEffectivenessRate', descending: true }],
      ...overrides
    };
  }

  /**
   * Build default advert search DTO
   */
  buildDefaultAdvertSearchDTO(overrides?: Partial<AdvertAnalyticsSearchDTO>): AdvertAnalyticsSearchDTO {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    return {
      offset: 0,
      limit: 25,
      filters: {
        fromDate: thirtyDaysAgo,
        toDate: new Date()
      },
      order: [{ column: 'notificationEffectivenessRate', descending: true }],
      ...overrides
    };
  }

  /**
   * Get effectiveness badge class for MDBootstrap
   */
  getEffectivenessBadgeClass(rate: number): string {
    if (rate >= 70) return 'badge-success';
    if (rate >= 50) return 'badge-warning';
    if (rate >= 30) return 'badge-info';
    return 'badge-danger';
  }

  /**
   * Get effectiveness rating text
   */
  getEffectivenessRating(rate: number): string {
    if (rate >= 80) return 'Highly Effective';
    if (rate >= 60) return 'Effective';
    if (rate >= 40) return 'Moderate';
    if (rate >= 20) return 'Poor';
    return 'Very Poor';
  }

  /**
   * Get advert status badge class for MDBootstrap
   */
  getAdvertStatusBadgeClass(statusId: number): string {
    switch (statusId) {
      case AdvertStatusEnum.Active: return 'badge-success';
      case AdvertStatusEnum.Inactive: return 'badge-secondary';
      case AdvertStatusEnum.Expired: return 'badge-warning';
      case AdvertStatusEnum.Ended: return 'badge-danger';
      default: return 'badge-light';
    }
  }

  /**
   * Get sold status badge class for MDBootstrap
   */
  getSoldStatusBadgeClass(statusId: number): string {
    switch (statusId) {
      case SoldStatusEnum.Active: return 'badge-success';
      case SoldStatusEnum.Sold: return 'badge-primary';
      case SoldStatusEnum.Unsold: return 'badge-secondary';
      case SoldStatusEnum.Draft: return 'badge-warning';
      case SoldStatusEnum.Provisional: return 'badge-info';
      case SoldStatusEnum.Withdrawn: return 'badge-dark';
      default: return 'badge-light';
    }
  }

  /**
   * Get advert status text from status ID
   */
  getAdvertStatusText(statusId: number): string {
    switch (statusId) {
      case AdvertStatusEnum.Active: return 'Active';
      case AdvertStatusEnum.Inactive: return 'Inactive';
      case AdvertStatusEnum.Expired: return 'Expired';
      case AdvertStatusEnum.Ended: return 'Ended';
      default: return 'Unknown';
    }
  }

  /**
   * Get sold status text from status ID
   */
  getSoldStatusText(statusId: number): string {
    switch (statusId) {
      case SoldStatusEnum.Active: return 'Active';
      case SoldStatusEnum.Sold: return 'Sold';
      case SoldStatusEnum.Unsold: return 'Unsold';
      case SoldStatusEnum.Draft: return 'Draft';
      case SoldStatusEnum.Provisional: return 'Provisional';
      case SoldStatusEnum.Withdrawn: return 'Withdrawn';
      default: return 'Unknown';
    }
  }


  /**
   * Get health status class
   */
  getHealthStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'healthy': return 'text-success';
      case 'warning': return 'text-warning';
      case 'critical': return 'text-danger';
      default: return 'text-secondary';
    }
  }

  private downloadFile(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  private getDateString(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }
}
