import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {AdminRoutingModule} from './admin-routing.module';
import {AdminComponent} from "../components/admin/admin.component";
import {
  VehicleLookupImporterComponent
} from "../components/admin/vehicle/vehicle-lookup-importer/vehicle-lookup-importer.component";
import {AdminVehicleCheckerComponent} from "../components/admin/vehicle/vehicle-checker/vehicle-checker.component";
import {AdminVehicleViewComponent} from "../components/admin/vehicle/vehicle-view/vehicle-view.component";
import {AdminVehicleSearchComponent} from "../components/admin/vehicle/vehicle-search/vehicle-search.component";
import {AdminVehicleDataComponent} from "../components/admin/vehicle/vehicle-data/vehicle-data.component";
import {AdminDashboardComponent} from "../components/admin/dashboard/admin-dashboard.component";
import {CustomerEditComponent} from "../components";
import {AdminCustomerViewComponent} from "../components/admin/customer/customer-view/customer-view.component";
import {CustomerSearchComponent} from "../components/admin/customer/customer-search/customer-search.component";
import {MmdComponent} from "../components/admin/maintenance/alt/mmd/mmd.component";
import {AttribMaintComponent} from "../components/admin/maintenance/attrib/attrib.component";
import {AttribvalMaintComponent} from "../components/admin/maintenance/attribval/attribval.component";
import {CountryProductComponent} from "../components/admin/maintenance/country-product/country-product.component";
import {CountryComponent} from "../components/admin/maintenance/country/country.component";
import {ProductComponent} from "../components/admin/maintenance/product/product.component";
import {TaxComponent} from "../components/admin/maintenance/tax/tax.component";
import {CustomerNotes2Component} from "../components/admin/customer/customer-notes2/customer-notes2.component";
import {SiteMaintComponent} from "../components/admin/maintenance/site/site.component";
import {PlatformComponent} from "../components/admin/maintenance/platform/platform.component";
import {AdminSaleSearchComponent} from "../components/admin/sale/sale-search/sale-search.component";
import {AdminSaleEditComponent} from "../components/admin/sale/sale-edit/sale-edit.component";
import {AdminSaleViewComponent} from "../components/admin/sale/sale-view/sale-view.component";
import {CustomerContactsComponent} from "../components/admin/customer/customer-contacts/customer-contacts.component";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {HttpClientModule} from "@angular/common/http";
import {ImageCropperComponent} from "ngx-image-cropper";
import {NgsgModule} from "ng-sortgrid";
import {NgxGoogleAnalyticsModule, NgxGoogleAnalyticsRouterModule} from "ngx-google-analytics";
import {DndModule} from "ngx-drag-drop";
import {NgxPaginationModule} from "ngx-pagination";
import {RouterModule} from "@angular/router";
import {MDBBootstrapModulesPro} from "ng-uikit-pro-standard";
import {
  AdminAddressService,
  AdminAdvertService,
  AdminBidService,
  AdminContactActionService,
  AdminCustomerService,
  AdminFaultCheckService,
  AdminNegotiationService, AdminOfferService,
  AdminSaleService,
  AdminStatDataService,
  AdminWatchlistService,
  AdminWhosWhoService,
  AdminVoipService, AdminMovementService, AdminAccountsService, AdminXeroAuthService,
  AdminSearchStatsService
} from '../components/admin/services';
import {DragDropModule} from "@angular/cdk/drag-drop";
import {ManageAuctionsComponent} from '../components/admin/sale/manage-auctions/manage-auctions.component';
import {AdminSaleAuctioneerComponent} from "../components/admin/sale/sale-auctioneer/sale-auctioneer.component";
import {AuctionListComponent} from '../components/admin/sale/auction-list/auction-list.component';
import {LotListComponent} from '../components/admin/sale/lot-list/lot-list.component';
import {ManageNegotiationsComponent} from '../components/admin/sale/manage-negotiations/manage-negotiations.component';
import {AdminAttendeesComponent} from '../components/admin/sale/attendees/admin-attendees.component';
import {InvoicesComponent} from '../components/admin/accounts/invoices/invoices.component';
import {RostrumMessengerComponent} from "../components/admin/sale/rostrum-messenger/rostrum-messenger.component";
import {UnreconciledComponent} from "../components/admin/accounts/unreconciled/unreconciled.component";
import {CustomerActionsComponent} from "../components/admin/customer/customer-actions/customer-actions.component";
import {VehicleHistoryComponent} from "../components/admin/vehicle/vehicle-history/vehicle-history.component";
import {AdminSaleProfilesComponent} from '../components/admin/sale/profiles/admin-sale-profiles.component';
import {
  AdminAdvertNoteService,
  BidService,
  AdminBrokerageService,
  CustomerAttribService,
  DealService,
  MakeModelDerivService,
  AdminProspectService
} from '../services';
import {SuggestedVehiclesComponent} from '../components/admin/sale/suggested-vehicles/suggested-vehicles.component';
import {ColorPickerModule} from "ngx-color-picker";
import {MainModule} from "./main.module";
import {
  AdminContactService,
  AdminCustomerInternalInfoService,
  AdminValuationService,
  ExternalEventQueueService
} from "../global/services";
import {HttpModule} from "../global/modules";
import {LoggedinComponentModule} from "./common/index";
import {MileagePipe} from "../global/pipes";
import {OffersSearchComponent} from '../components/admin/offers-search/offers-search.component';
import {DealSearchComponent} from '../components/admin/deal/deal-search/deal-search.component';
import {AdvertAdminViewComponent} from '../components/admin/advert-admin-view/advert-admin-view.component';
import {FaultCheckMaintComponent} from "../components/admin/maintenance/fault-check/fault-check-maint.component";
import {
  FaultCheckStatusMaintComponent
} from "../components/admin/maintenance/fault-check/fault-check-status/fault-check-status-maint.component";
import {
  FaultCheckCategoryMaintComponent
} from "../components/admin/maintenance/fault-check/fault-check-category/fault-check-category-maint.component";
import {
  FaultCheckItemMaintComponent
} from "../components/admin/maintenance/fault-check/fault-check-item/fault-check-item-maint.component";
import {
  FaultCheckTypeMaintComponent
} from "../components/admin/maintenance/fault-check/fault-check-type/fault-check-type-maint.component";
import {WhosWhoComponent} from "../components/admin/whos-who/whos-who.component";
import {WhosWhoMainComponent} from "../components/admin/whos-who/whos-who-main/whos-who-main.component";
import {AdminAdviewsComponent} from "../components/admin/admin-adviews/admin-adviews.component";
import {
  CustomerAdviewAdvertsComponent
} from "../components/admin/customer-adviews-adverts/customer-adview-adverts.component";
import {AdminSearchViewComponent} from "../components/admin/admin-search-view/admin-search-view.component";
import {
  CustomerSearchesViewComponent
} from "../components/admin/customer-searches-view/customer-searches-view.component";
import {AdminLoginsViewComponent} from "../components/admin/admin-logins-view/admin-logins-view.component";
import {CustomerLoginsViewComponent} from "../components/admin/customer-logins-view/customer-logins-view.component";
import {
  CustomerAdvertisingTotalsViewComponent
} from "../components/admin/customer-advertising-totals-view/customer-advertising-totals-view.component";
import {ContactLoginsViewComponent} from "../components/admin/contact-logins-view/contact-logins-view.component";
import {AdminWatchlistViewComponent} from "../components/admin/admin-watchlist-view/admin-watchlist-view.component";
import {
  CustomerWatchlistsAdvertsComponent
} from "../components/admin/customer-watchlists-adverts/customer-watchlists-adverts.component";
import {ManageBrokeragesComponent} from "../components/admin/manage-brokerages/manage-brokerages.component";
import {ProspectsViewComponent} from "../components/admin/sale/prospects-view/prospects-view.component";
import {
  AdvertNegotiationViewComponent
} from "../components/admin/sale/advert-negotiation-view/advert-negotiation-view.component";
import {ResizeObserverDirective} from "../directives/resize-observer.directive";
import {AdvertNotesComponent} from "../components/admin/sale/advert-notes/advert-notes.component";
import {ContactSearchComponent} from "../components/admin/contact-search/contact-search.component";
import {AdminTaskService} from "../components/admin/services/admin-task.service";
import {AdminTasksViewComponent} from "../components/admin/admin-tasks-view/admin-tasks-view.component";
import {
  SelectAssigneeDialogComponent
} from "../components/admin/select-assignee-dialog/select-assignee-dialog.component";
import {AdminCallRecordsComponent} from "../components/admin/admin-call-records/admin-call-records.component";
import {SiteAdminSearchComponent} from "../components/admin/site-admin/site-admin-search.component";
import {AdminContactEditComponent} from "../components/admin/admin-contact-edit/admin-contact-edit.component";
import {AdminCallStatsComponent} from "../components/admin/admin-call-records/admin-call-stats.component";
import {AdminCallStatsChartComponent} from "../components/admin/admin-call-records/admin-call-stats-chart.component";
import {
  AdminCallStatsDualChartComponent
} from "../components/admin/admin-call-records/admin-call-stats-dual-chart.component";
import {AdminCallStatsDetailComponent} from "../components/admin/admin-call-records/admin-call-stats-detail.component";
import {AdminDashboardTrendlineChart} from "../components/admin/dashboard/admin-dashboard-trendline-chart";
import {AdminDashboardBarChart} from "../components/admin/dashboard/admin-dashboard-bar-chart";
import {NgxJsonViewerModule} from "ngx-json-viewer";
import {NgModelWatchDirective} from '../global/directives';
import {
  AdminMessagesToSellersComponent
} from "../components/admin/admin-messages-to-sellers/admin-messages-to-sellers.component";
import {NgToggleModule} from "ng-toggle-button";
import {VehicleMovementComponent} from "../components/admin/vehicle-movement/vehicle-movement.component";
import {BookMovementButtonComponent} from "../components/admin/vehicle-movement/book-movement-button.component";
import {
  AdminAssignCustomerDialogComponent
} from "../components/admin/admin-assign-customer-dialog/admin-assign-customer-dialog.component";
import {
  AdminCallStatsPerDayComponent
} from "../components/admin/admin-call-records/admin-call-stats-by-day/admin-call-stats-by-day.component";
import {
  AdminCallStatsFilterComponent
} from "../components/admin/admin-call-records/admin-call-stats-filter/admin-call-stats-filter.component";
import {
  AdminCallStatChartComponent
} from "../components/admin/admin-call-records/admin-call-stat-chart/admin-call-stat-chart.component";
import {
  AdminUnverifiedContactsComponent
} from "../components/admin/admin-unverified-contacts/admin-unverified-contacts.component";
import {AccountsDataComponent} from "../components/admin/accounts/accounts-data/accounts-data.component";
import {AdminSimpleImportService} from "../components/admin/services/admin-simple-import.service";
import {SimpleImporterComponent} from "../components/admin/import/simple-importer/simple-importer.component";
import {VehicleManagementModule} from "../components/admin/vehicle/vehicle-import-management/vehicle-management.module";
import {
  StaffCallRatioChartsComponent
} from "../components/admin/dashboard/staff-call-ratio-charts/staff-call-ratio-charts.component";
import {NgChartsModule} from 'ng2-charts';
import {AdminAccountCreatorComponent} from "../components/admin/admin-account-creator/admin-account-creator.component";
import {AdminExtLeadComponent} from "../components/admin/admin-ext-leads/admin-ext-lead.component";
import {AdminExtLeadService} from "../components/admin/services/admin-ext-lead.service";
import {
  AdminAdvertisingElsewhereComponent
} from "../components/admin/admin-advertising-elsewhere/admin-advertising-elsewhere.component";
import {AdminKpisComponent} from "../components/admin/dashboard/kpis/admin-kpis.component";
import {
  InvoiceCustomerOrdersComponent
} from "../components/admin/accounts/invoices/invoice-customer-orders/invoice-customer-orders.component";
import {OrderLinesComponent} from "../components/admin/accounts/invoices/order-lines/order-lines.component";
import {InventUsersComponent} from "../components/admin/invent-users/invent-users.component";
import {InventUserService} from "../components/admin/services/invent-user.service";
import {AdminSearchStatsDashboardComponent} from "../components/admin/admin-search-stats/admin-search-stats-dashboard.component";
import {SearchAnalyticsMainComponent} from "../components/admin/admin-search-stats/search-analytics-main.component";
import {CustomerAnalyticsComponent} from "../components/admin/admin-search-stats/customer-analytics.component";
import { AdvertAnalyticsComponent } from '../components/admin/admin-search-stats/advert-analytics.component';
import { EmailContactIssuesComponent } from '../components/admin/admin-search-stats/email-contact-issues.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    ReactiveFormsModule,
    MDBBootstrapModulesPro,
    ImageCropperComponent,
    ColorPickerModule,
    NgxGoogleAnalyticsModule,
    NgxGoogleAnalyticsRouterModule,
    LoggedinComponentModule,
    DndModule,
    NgsgModule,
    NgxPaginationModule,
    HttpModule,
    HttpClientModule,
    ReactiveFormsModule,
    AdminRoutingModule,
    DragDropModule,
    MainModule,
    NgxJsonViewerModule,
    NgToggleModule,
    NgChartsModule,
    NgToggleModule,
    VehicleManagementModule
  ],
  declarations: [
    ResizeObserverDirective,
    AdminComponent,
    VehicleLookupImporterComponent,
    AdminVehicleCheckerComponent,
    AdminVehicleDataComponent,
    AdminAssignCustomerDialogComponent,
    AdminVehicleViewComponent,
    AdminVehicleSearchComponent,
    AdminDashboardComponent,
    AdminKpisComponent,
    CustomerEditComponent,
    AdminCustomerViewComponent,
    CustomerSearchComponent,
    MmdComponent,
    AttribMaintComponent,
    AttribvalMaintComponent,
    CountryProductComponent,
    CountryComponent,
    FaultCheckMaintComponent,
    FaultCheckTypeMaintComponent,
    FaultCheckStatusMaintComponent,
    FaultCheckCategoryMaintComponent,
    FaultCheckItemMaintComponent,
    ProductComponent,
    TaxComponent,
    SiteMaintComponent,
    PlatformComponent,
    CustomerNotes2Component,
    AdminSaleSearchComponent,
    AdminSaleEditComponent,
    AdminSaleViewComponent,
    AdminSaleEditComponent,
    AdminSaleAuctioneerComponent,
    AdminAccountCreatorComponent,
    ManageAuctionsComponent,
    AuctionListComponent,
    LotListComponent,
    ManageNegotiationsComponent,
    ManageBrokeragesComponent,
    AdminAttendeesComponent,
    InvoicesComponent,
    InvoiceCustomerOrdersComponent,
    OrderLinesComponent,
    RostrumMessengerComponent,
    UnreconciledComponent,
    CustomerActionsComponent,
    VehicleHistoryComponent,
    AdminSaleProfilesComponent,
    SuggestedVehiclesComponent,
    NgModelWatchDirective,
    CustomerContactsComponent,
    OffersSearchComponent,
    DealSearchComponent,
    AdvertAdminViewComponent,
    AdminAdviewsComponent,
    WhosWhoComponent,
    WhosWhoMainComponent,
    CustomerAdviewAdvertsComponent,
    AdminSearchViewComponent,
    CustomerSearchesViewComponent,
    AdminLoginsViewComponent,
    CustomerLoginsViewComponent,
    CustomerAdvertisingTotalsViewComponent,
    ContactLoginsViewComponent,
    AdminWatchlistViewComponent,
    CustomerWatchlistsAdvertsComponent,
    ProspectsViewComponent,
    AdvertNegotiationViewComponent,
    AdvertNotesComponent,
    ContactSearchComponent,
    AdminTasksViewComponent,
    SelectAssigneeDialogComponent,
    AdminCallRecordsComponent,
    SiteAdminSearchComponent,
    AdminContactEditComponent,
    AdminCallStatsComponent,
    AdminCallStatsChartComponent,
    AdminCallStatsDualChartComponent,
    AdminCallStatsDetailComponent,
    AdminDashboardTrendlineChart,
    AdminDashboardBarChart,
    AdminMessagesToSellersComponent,
    VehicleMovementComponent,
    BookMovementButtonComponent,
    AdminCallStatsPerDayComponent,
    AdminCallStatsFilterComponent,
    AdminCallStatChartComponent,
    AdminUnverifiedContactsComponent,
    AccountsDataComponent,
    SimpleImporterComponent,
    StaffCallRatioChartsComponent,
    AdminExtLeadComponent, AdminAdvertisingElsewhereComponent,
    InventUsersComponent,
    AdminSearchStatsDashboardComponent,
    SearchAnalyticsMainComponent,
    AdminSearchStatsDashboardComponent,
    CustomerAnalyticsComponent,
    AdvertAnalyticsComponent,
    EmailContactIssuesComponent
  ],
  providers: [AdminContactService, AdminAdvertService, AdminFaultCheckService,
    AdminExtLeadService,
    AdminSaleService, AdminAddressService, AdminStatDataService, CustomerAttribService,
    MakeModelDerivService, AdminValuationService, ExternalEventQueueService, AdminContactActionService, AdminCustomerService,
    AdminNegotiationService, DealService, BidService, MileagePipe, AdminWhosWhoService, AdminCustomerInternalInfoService,
    AdminWatchlistService, AdminBrokerageService, AdminProspectService, AdminAdvertNoteService, AdminBidService,
    AdminOfferService, AdminTaskService, AdminVoipService, AdminMovementService, AdminAccountsService, AdminSimpleImportService, AdminXeroAuthService, InventUserService, AdminSearchStatsService]
})

export class AdminModule {
}
