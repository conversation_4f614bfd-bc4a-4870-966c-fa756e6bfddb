import { BaseSearchDTO } from "../API";

export interface EmailContactIssueDTO {
  email: string;
  issueType: string; // unsubscribed, blocked, hardBounce, spam, etc.
  issueDescription: string;
  issueDate: Date;
  sender: string; // The sender that was blocked
}

export interface ContactIssueSearchDTO extends BaseSearchDTO {
  filters: ContactIssueSearchFiltersDTO;
}

export interface ContactIssueSearchFiltersDTO {
  startDate?: Date;
  endDate?: Date;
  issueType?: string; // Filter by specific issue type (unsubscribed, blocked, hardBounce, spam, etc.)
}
